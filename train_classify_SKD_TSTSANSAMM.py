import warnings
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)
"""
# SAMM数据集3分类的分组（基于测试样本数）
# 大样本组(>8个测试样本)
LARGE_SUBJECTS_SAMM = ['011', '006', '014', '026']
# 中等样本组(5-8个测试样本)
MEDIUM_SUBJECTS_SAMM = ['007', '035', '013', '016', '022', '033']
# 小样本组(3-4个测试样本)
SMALL_SUBJECTS_SAMM = ['009', '010', '017', '020', '012', '015', '018', '030', '032', '034', '021', '028']
# 极小样本组(1-2个测试样本)
VERY_SMALL_SUBJECTS_SAMM = ['019', '023', '024', '031', '036', '037']

# SAMM数据集训练顺序（按测试样本数量从多到少）
TRAINING_ORDER_SAMM = [
    '011', '006', '014', '026',  # 大样本组
    '007', '035', '013', '016', '022', '033',  # 中等样本组
    '009', '010', '017', '020', '012', '015', '018', '030', '032', '034', '021', '028',  # 小样本组
    '019', '023', '024', '031', '036', '037'  # 极小样本组
]

# SAMM数据集样本统计
SAMM_SAMPLE_STATS = {
    'positive': 26,   # 正面情绪
    'negative': 90,   # 负面情绪
    'surprise': 15,   # 惊讶情绪
    'total': 131      # 总样本数
}
"""
# 导入所需的库
import argparse  # 用于命令行参数解析
from distutils.util import strtobool  # 用于将字符串转换为布尔值
from train_classify_SKD_TSTSAN_functions_SAMM import main_SKD_TSTSAN_with_Aug_with_SKD, Logger  # 导入主训练函数和日志类
import sys
import smtplib
from email.mime.text import MIMEText
from email.header import Header
import datetime
import torch
import psutil
import platform
import os
import numpy as np
from sklearn.metrics import confusion_matrix
import time
import glob  # 用于文件模式匹配
from conv_selector import ConvolutionSelector
from cross_branch_selector import CrossBranchSelector
from learnable_tsm_selector import LearnableTSMSelector
from eca_selector import ECASelector
from activation_selector import ActivationSelector


class TriModalFusionSelector:
    """三模态特征融合选择器"""

    def __init__(self):
        self.fusion_options = {
            0: {
                'name': '传统特征拼接',
                'emoji': '📝',
                'description': '使用简单的concatenation进行特征融合',
                'params': '3.49M',
                'param_increase': '-',
                'increase_ratio': '-',
                'features': [
                    '基础的特征拼接操作',
                    '三个分支独立处理',
                    '最低内存占用',
                    '最快推理速度',
                    '基线方法'
                ]
            },
            1: {
                'name': '多头注意力融合',
                'emoji': '🧠',
                'description': '建立跨模态注意力关系，动态关注重要特征',
                'params': '4.08M',
                'param_increase': '+589K',
                'increase_ratio': '+16.9%',
                'features': [
                    '建立L、S、T三个模态的跨模态注意力',
                    '每个模态关注其他两个模态的重要信息',
                    '使用多头机制增强表达能力',
                    '平衡性能，适中开销',
                    '适合通用场景'
                ]
            },
            2: {
                'name': '门控融合',
                'emoji': '🚪',
                'description': '自适应调整模态权重，轻量级高效设计',
                'params': '4.33M',
                'param_increase': '+836K',
                'increase_ratio': '+23.9%',
                'features': [
                    '通过门控机制动态调整模态重要性',
                    '自适应控制不同模态特征贡献度',
                    '轻量级设计，计算效率高',
                    '适合资源受限环境',
                    '快速收敛'
                ]
            },
            3: {
                'name': '高阶交互融合',
                'emoji': '🔬',
                'description': '捕获复杂模态交互，三阶张量建模',
                'params': '14.18M',
                'param_increase': '+10.69M',
                'increase_ratio': '+306.3%',
                'features': [
                    '使用双线性池化捕获二阶模态交互',
                    '通过Tucker分解近似三阶张量交互',
                    '建模复杂的非线性模态关系',
                    '最强表达力，参数较多',
                    '适合追求极致性能'
                ]
            },
            4: {
                'name': '集成融合 ⭐⭐ (推荐)',
                'emoji': '🏆',
                'description': '结合多种融合策略，自适应权重平衡，最佳性能表现',
                'params': '15.66M',
                'param_increase': '+12.17M',
                'increase_ratio': '+348.8%',
                'features': [
                    '🔥 结合注意力、门控、高阶三种融合策略',
                    '⚖️ 通过可学习权重自动平衡不同策略',
                    '🚀 最佳性能表现，显著提升准确率',
                    '🎯 自适应选择最优融合方式',
                    '💎 推荐用于最终部署'
                ]
            }
        }

    def display_options(self):
        """显示融合选项"""
        print("\n" + "="*80)
        print(f"{colorize('🔧 三模态特征融合选择器', Colors.BOLD + Colors.BRIGHT_CYAN)}")
        print("="*80)

        for option_id, option in self.fusion_options.items():
            option_title = f"{option_id + 1}. {option['emoji']} {option['name']}"
            print(f"\n{colorize(option_title, Colors.BRIGHT_YELLOW)}")
            print(f"   📝 描述: {option['description']}")
            print(f"   📊 参数量: {option['params']}")
            print(f"   📈 参数增加: {option['param_increase']}")
            print(f"   📊 增加比例: {option['increase_ratio']}")
            print(f"   🔍 特征:")
            for feature in option['features']:
                print(f"      • {feature}")

        print("\n" + "="*80)
        print(f"{colorize('💡 推荐说明:', Colors.BRIGHT_GREEN)}")
        print(f"   📝 选择1: 基线方法，快速验证")
        print(f"   🧠 选择2: 平衡性能与效率，通用推荐")
        print(f"   🚪 选择3: 轻量级，适合资源受限")
        print(f"   🔬 选择4: 追求极致性能")
        print(f"   🏆 选择5: 最佳综合性能 ⭐⭐ 强烈推荐")
        print("="*80)
        print(f"{colorize('🔥 核心优势: 选择2-5可显著提升微表情识别准确率！', Colors.BRIGHT_MAGENTA)}")
        print("="*80)

    def get_fusion_config(self, choice):
        """获取融合配置"""
        if choice == 1:  # 传统拼接
            return {
                'use_trimodal_fusion': False,
                'trimodal_fusion_type': 'none',
                'trimodal_num_heads': 8,
                'suffix': ''
            }
        elif choice == 2:  # 注意力融合
            return {
                'use_trimodal_fusion': True,
                'trimodal_fusion_type': 'attention',
                'trimodal_num_heads': 8,
                'suffix': '_attn'
            }
        elif choice == 3:  # 门控融合
            return {
                'use_trimodal_fusion': True,
                'trimodal_fusion_type': 'gated',
                'trimodal_num_heads': 8,
                'suffix': '_gate'
            }
        elif choice == 4:  # 高阶交互
            return {
                'use_trimodal_fusion': True,
                'trimodal_fusion_type': 'high_order',
                'trimodal_num_heads': 8,
                'suffix': '_high'
            }
        elif choice == 5:  # 集成融合
            return {
                'use_trimodal_fusion': True,
                'trimodal_fusion_type': 'ensemble',
                'trimodal_num_heads': 8,
                'suffix': '_ensemble'
            }
        else:
            return self.get_fusion_config(1)  # 默认传统拼接

    def get_fusion_name(self, choice):
        """获取融合方法名称"""
        if 1 <= choice <= 5:
            return self.fusion_options[choice-1]['name'], self.fusion_options[choice-1]['emoji']
        return "传统特征拼接", "📝"

# 颜色和样式定义
class Colors:
    """ANSI颜色代码"""
    # 基础颜色
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'

    # 亮色
    BRIGHT_BLACK = '\033[90m'
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'

    # 样式
    BOLD = '\033[1m'
    DIM = '\033[2m'
    UNDERLINE = '\033[4m'
    BLINK = '\033[5m'
    REVERSE = '\033[7m'
    STRIKETHROUGH = '\033[9m'

    # 重置
    RESET = '\033[0m'
    END = '\033[0m'
def colorize(text, color_code):
    """给文本添加颜色"""
    return f"{color_code}{text}{Colors.RESET}"

def print_banner(text, color=Colors.BRIGHT_CYAN, emoji="🎉"):
    """打印横幅"""
    border = "=" * (len(text) + 4)
    print(f"\n{colorize(border, color)}")
    print(f"{colorize(f'{emoji} {text} {emoji}', Colors.BOLD + color)}")
    print(f"{colorize(border, color)}\n")

def print_section(title, color=Colors.BRIGHT_BLUE, emoji="📋"):
    """打印章节标题"""
    print(f"\n{colorize('─' * 50, color)}")
    print(f"{colorize(f'{emoji} {title}', Colors.BOLD + color)}")
    print(f"{colorize('─' * 50, color)}")

def print_info(text, emoji="ℹ️"):
    """打印信息"""
    print(f"{colorize(f'{emoji} {text}', Colors.CYAN)}")

def print_success(text, emoji="✅"):
    """打印成功信息"""
    print(f"{colorize(f'{emoji} {text}', Colors.BRIGHT_GREEN)}")

def print_warning(text, emoji="⚠️"):
    """打印警告信息"""
    print(f"{colorize(f'{emoji} {text}', Colors.BRIGHT_YELLOW)}")

def print_error(text, emoji="❌"):
    """打印错误信息"""
    print(f"{colorize(f'{emoji} {text}', Colors.BRIGHT_RED)}")

def print_highlight(text, emoji="⭐"):
    """打印高亮信息"""
    print(f"{colorize(f'{emoji} {text}', Colors.BOLD + Colors.BRIGHT_MAGENTA)}")


def scan_and_select_pretrained_model(pretrained_dir="/home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model"):
    """
    扫描预训练模型目录并让用户选择模型

    Args:
        pretrained_dir (str): 预训练模型目录路径

    Returns:
        str: 选择的模型路径，如果取消选择则返回None
    """
    print("\n" + "="*80)
    print("🔍 正在扫描预训练模型文件...")
    print("="*80)

    # 扫描.pth文件
    pth_pattern = os.path.join(pretrained_dir, "*.pth")
    pth_files = glob.glob(pth_pattern)

    if not pth_files:
        print(f"❌ 在目录 {pretrained_dir} 中未找到任何.pth文件")
        return None

    # 按文件名排序
    pth_files.sort()

    print(f"📁 在目录 {pretrained_dir} 中找到 {len(pth_files)} 个预训练模型:")
    print("-"*80)

    # 显示文件列表
    for i, pth_file in enumerate(pth_files, 1):
        filename = os.path.basename(pth_file)
        try:
            # 获取文件大小
            file_size = os.path.getsize(pth_file) / (1024 * 1024)  # MB
            # 获取修改时间
            mod_time = datetime.datetime.fromtimestamp(os.path.getmtime(pth_file))
            mod_time_str = mod_time.strftime("%Y-%m-%d %H:%M")

            print(f" {i}. {filename}")
            print(f"    📊 大小: {file_size:.1f} MB  📅 修改时间: {mod_time_str}")
            print()
        except Exception as e:
            print(f" {i}. {filename}")
            print(f"    ⚠️ 无法获取文件信息: {str(e)}")
            print()

    print("-"*80)
    print("0. 取消选择，使用默认路径")
    print("="*80)

    # 用户选择
    while True:
        try:
            choice = input(f"\n请选择预训练模型 (0-{len(pth_files)}): ").strip()

            if choice == "0":
                print("✅ 已取消选择，将使用默认预训练模型路径")
                return None

            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(pth_files):
                selected_path = pth_files[choice_idx]
                selected_filename = os.path.basename(selected_path)
                print(f"✅ 已选择: {selected_filename}")
                print(f"📍 完整路径: {selected_path}")
                return selected_path
            else:
                print(f"❌ 无效选择，请输入0-{len(pth_files)}之间的数字")

        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n\n❌ 用户取消操作")
            return None


def get_conv_type_name(conv_type):
    """根据卷积类型数字返回名称"""
    conv_names = {
        1: "标准卷积",
        2: "风车形卷积",
        3: "小波变换卷积",
        4: "小波变换感受野注意力卷积",
        5: "小波变换风车形感受野注意力卷积",
        6: "小波变换风车形感受野注意力卷积(轻量化)"
    }
    return conv_names.get(conv_type, f"未知类型({conv_type})")


def get_system_info():
    """获取系统信息"""
    gpu_info = "❌ 不可用"
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_info = f"🚀 {gpu_name} ({gpu_memory:.1f}GB)"

    return {
        "OS": f"💻 {platform.system()} {platform.release()}",
        "CPU": f"⚡ {psutil.cpu_count()}核心处理器",
        "内存": f"🧠 {psutil.virtual_memory().total / (1024**3):.1f}GB RAM",
        "GPU": gpu_info
    }

def format_time(seconds):
    """格式化时间"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

def format_subject_results(results_dict):
    """格式化受试者结果"""
    if 'subject_results' not in results_dict:
        return "", ""
        
    # 简要结果
    brief_results = "\n【各受试者评估结果】\n"
    brief_results += "-" * 50 + "\n"
    
    # 添加每个受试者的单独结果
    for subject_id, data in sorted(results_dict['subject_results'].items()):
        uf1_val = data.get('UF1', 0.0)
        uar_val = data.get('UAR', 0.0)
        brief_results += f"受试者 {subject_id}: UF1={uf1_val:.4f}, UAR={uar_val:.4f}\n"
    
    brief_results += "-" * 50 + "\n"
    
    # 添加表格形式的详细统计
    brief_results += f"\n详细统计:\n"
    brief_results += f"{'受试者':^10} {'UF1':^10} {'UAR':^10} {'准确率':^10}\n"
    brief_results += "-" * 50 + "\n"
    
    # 计算平均值
    uf1_values = []
    uar_values = []
    acc_values = []
    
    for subject_id, data in sorted(results_dict['subject_results'].items()):
        uf1 = data.get('UF1', 0.0)
        uar = data.get('UAR', 0.0)

        # 安全计算准确率
        pred_list = data.get('pred', [])
        truth_list = data.get('truth', [])

        if pred_list and truth_list and len(pred_list) == len(truth_list):
            correct = sum(1 for p, t in zip(pred_list, truth_list) if p == t)
            total = len(pred_list)
            acc = correct / total if total > 0 else 0
        else:
            acc = 0.0

        brief_results += f"{subject_id:^10} {uf1:^10.4f} {uar:^10.4f} {acc:^10.4f}\n"

        uf1_values.append(uf1)
        uar_values.append(uar)
        acc_values.append(acc)
    
    # 添加平均值（注意：这是各受试者指标的算术平均，不等同于总体指标）
    brief_results += "-" * 50 + "\n"
    brief_results += f"{'算术平均':^10} {np.mean(uf1_values):^10.4f} {np.mean(uar_values):^10.4f} {np.mean(acc_values):^10.4f}\n"
    brief_results += f"\n{'注意':^10} 算术平均值仅供参考，总体性能以上方报告的UF1/UAR为准\n"
    
    # 详细结果部分
    detailed_results = "\n【各受试者详细预测结果】\n"
    detailed_results += "=" * 50 + "\n"
    
    # 混淆矩阵统计
    all_pred = []
    all_truth = []
    
    for subject_id, data in sorted(results_dict['subject_results'].items()):
        detailed_results += f"\n受试者 {subject_id}:\n"

        # 安全获取性能指标
        uf1_val = data.get('UF1', 0.0)
        uar_val = data.get('UAR', 0.0)
        detailed_results += f"性能指标: UF1={uf1_val:.4f}, UAR={uar_val:.4f}\n"

        # 安全获取预测和真实标签
        pred_list = data.get('pred', [])
        truth_list = data.get('truth', [])

        if pred_list and truth_list:
            pred = np.array(pred_list)
            truth = np.array(truth_list)
            all_pred.extend(pred)
            all_truth.extend(truth)
        else:
            detailed_results += "警告: 该受试者缺少预测或真实标签数据\n"
            continue
        
        detailed_results += "\n类别统计:\n"
        # 根据分类数选择情绪标签
        if len(np.unique(np.concatenate([pred, truth]))) <= 3:
            emotion_dict = {'正性': 0, '负性': 1, '惊讶': 2}
        else:
            emotion_dict = {'快乐': 0, '惊讶': 1, '厌恶': 2, '压抑': 3, '其他': 4}

        for emotion, idx in emotion_dict.items():
            mask = (truth == idx)
            if np.any(mask):
                class_acc = np.mean(pred[mask] == truth[mask])
                class_count = np.sum(mask)
                detailed_results += f"- {emotion}: {class_acc:.4f} ({np.sum(pred[mask] == truth[mask])}/{class_count})\n"
        
        detailed_results += f"\n标签对比:\n"
        detailed_results += f"预测标签: {pred_list}\n"
        detailed_results += f"真实标签: {truth_list}\n"
        detailed_results += "-" * 50 + "\n"
    
    return brief_results, detailed_results

def format_metric(value):
    """安全格式化指标值"""
    if value is None:
        return 'N/A'
    try:
        return f"{float(value):.4f}"
    except (ValueError, TypeError):
        return str(value)

def validate_results_dict(results_dict):
    """验证results_dict的完整性"""
    issues = []

    if not isinstance(results_dict, dict):
        issues.append("results_dict不是字典类型")
        return issues

    # 检查必需的键
    required_keys = ['status', 'subject_results', 'emotion_acc', 'UF1', 'UAR']
    for key in required_keys:
        if key not in results_dict:
            issues.append(f"缺少必需的键: {key}")

    # 检查subject_results
    if 'subject_results' in results_dict:
        if not isinstance(results_dict['subject_results'], dict):
            issues.append("subject_results不是字典类型")
        else:
            for subject_id, data in results_dict['subject_results'].items():
                if not isinstance(data, dict):
                    issues.append(f"受试者{subject_id}的数据不是字典类型")
                    continue

                # 检查每个受试者的必需数据
                required_subject_keys = ['pred', 'truth', 'UF1', 'UAR']
                for key in required_subject_keys:
                    if key not in data:
                        issues.append(f"受试者{subject_id}缺少键: {key}")

    # 检查emotion_acc
    if 'emotion_acc' in results_dict:
        if not isinstance(results_dict['emotion_acc'], dict):
            issues.append("emotion_acc不是字典类型")

    return issues

def send_training_results(config, results_dict, total_time):
    """发送训练结果邮件并在终端显示内容"""
    if not config.email_notify:
        print("\n【邮件通知】邮件通知功能未启用")
        return

    if not all([config.email_sender, config.email_receiver, config.email_password]):
        print("\n【邮件通知】邮件配置不完整,请检查以下参数:")
        print("- email_sender")
        print("- email_receiver")
        print("- email_password")
        return

    try:
        # 验证results_dict的完整性
        validation_issues = validate_results_dict(results_dict)
        if validation_issues:
            print("\n【邮件通知】results_dict验证失败:")
            for issue in validation_issues:
                print(f"  - {issue}")
            print("将尝试发送部分可用的结果...")

        sys_info = get_system_info()
        brief_results, detailed_results = format_subject_results(results_dict)
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 判断是否是单个受试者的结果
        is_single_subject = len(results_dict.get('subject_results', {})) == 1
        subject_id = list(results_dict.get('subject_results', {}).keys())[0] if is_single_subject else None

        # 安全获取指标值
        uf1_value = format_metric(results_dict.get('UF1'))
        uar_value = format_metric(results_dict.get('UAR'))

        # 确定分类方案信息
        class_num = getattr(config, 'class_num', 5)
        if class_num == 3:
            class_scheme = "3分类 (正性、负性、惊讶)"
        else:
            class_scheme = "5分类 (快乐、惊讶、厌恶、压抑、其他)"

        # 生成内容
        content = f"""
{'='*80}
【实验结果报告 - {class_scheme}】
{'='*80}

【基本信息】
实验名称: {config.exp_name}
分类方案: {class_scheme}
时间: {current_time}
{'受试者训练时间' if is_single_subject else '总训练时间'}: {format_time(total_time)}
数据集: {os.path.basename(config.main_path)}
{f'当前受试者: {subject_id}' if is_single_subject else ''}

【系统环境】
操作系统: {sys_info['OS']}
处理器: {sys_info['CPU']}
内存: {sys_info['内存']}
GPU: {sys_info['GPU']}
GPU数量: {torch.cuda.device_count() if torch.cuda.is_available() else 0}
训练模式: {'多GPU并行' if torch.cuda.is_available() and torch.cuda.device_count() > 1 else '单设备'}

【模型配置】
模型架构: {config.model}
分类数量: {class_num}
是否使用预训练: {'是' if config.pre_trained else '否'}
预训练模型: {os.path.basename(config.pre_trained_model_path) if config.pre_trained else '无'}
是否使用COCO预训练: {'是' if config.Aug_COCO_pre_trained else '否'}
是否保存模型: {'是' if config.save_model else '否'}

【训练参数】
学习率: {config.learning_rate}
基础批次大小: {config.batch_size}
最大迭代次数: {config.max_iter}
损失函数: {config.loss_function}
随机种子: {config.seed}

【GPU性能配置】
CUDNN Benchmark: {'启用' if config.cudnn_benchmark else '禁用'}
CUDNN Deterministic: {'启用' if config.cudnn_deterministic else '禁用'}
混合精度训练: {'启用' if config.use_mixed_precision else '禁用'}
TF32加速: {'启用' if config.use_tf32 else '禁用'}
BatchNorm修复: {'启用' if getattr(config, 'use_batchnorm_fix', False) else '禁用'}
卷积类型: {get_conv_type_name(config.conv_type)}

【数据增强配置】
使用训练数据增强: {'是' if getattr(config, 'use_data_augmentation', False) else '否'}
使用测试数据增强: {'是' if getattr(config, 'use_test_augmentation', False) else '否'}
旋转角度范围: {getattr(config, 'aug_rotation_range', 'N/A')}
训练增强倍数: {getattr(config, 'aug_multipliers', 'N/A')}
测试增强倍数: {getattr(config, 'test_aug_multipliers', 'N/A')}
测试数据镜像训练: {'启用' if getattr(config, 'use_test_mirror_training', False) else '禁用'}
镜像训练受试者: {getattr(config, 'test_mirror_subjects', 'N/A')}

【蒸馏参数】
温度: {config.temperature}
α值: {config.alpha}
β值: {config.beta}
数据增强系数: {config.Aug_alpha}

【训练结果】
{'当前受试者' if is_single_subject else '总体'}性能:
- UF1分数: {uf1_value}
- UAR分数: {uar_value}

【各表情类别准确率】
{'-'*50}"""

        # 添加各表情类别的准确率
        emotion_acc = results_dict.get('emotion_acc', {})
        if emotion_acc:
            for emotion, acc in emotion_acc.items():
                content += f"\n- {emotion}: {format_metric(acc)}"
        else:
            content += "\n- 暂无各类别准确率数据"

        # 添加受试者结果
        if brief_results:
            content += f"\n\n{brief_results}"

        # 添加最优预测结果和原始标签对比
        subject_results = results_dict.get('subject_results', {})
        if subject_results:
            content += "\n\n【最优预测结果与原始标签对比】\n"
            content += "-" * 50 + "\n"

            for subj_id, data in sorted(subject_results.items()):
                content += f"\n受试者 {subj_id}:\n"
                content += f"预测标签: {data.get('pred', [])}\n"
                content += f"真实标签: {data.get('truth', [])}\n"
                content += f"UF1: {format_metric(data.get('UF1', 0))}, UAR: {format_metric(data.get('UAR', 0))}\n"
                content += "-" * 50 + "\n"

        # 如果是单个受试者的结果，添加详细结果
        if is_single_subject and detailed_results:
            content += f"\n{detailed_results}"

        content += f"\n{'='*80}\n"

        # 在终端显示内容
        print("\n【实验结果报告】")
        print("="*80)
        print(content)
        print("="*80)

        # 构建邮件
        message = MIMEText(content, 'plain', 'utf-8')
        if is_single_subject:
            subject_line = f'[{class_scheme}受试者结果] {config.exp_name} - {subject_id} - UF1={uf1_value}, UAR={uar_value}'
        else:
            subject_line = f'[{class_scheme}实验总结] {config.exp_name} - UF1={uf1_value}, UAR={uar_value}'

        message['Subject'] = Header(subject_line, 'utf-8')
        message['From'] = config.email_sender
        message['To'] = config.email_receiver

        print("\n【邮件通知】正在发送训练结果...")

        # 发送邮件
        try:
            smtp = smtplib.SMTP(config.smtp_server, config.smtp_port)
            smtp.starttls()

            try:
                smtp.login(config.email_sender, config.email_password)
            except smtplib.SMTPAuthenticationError:
                print("【错误】邮箱登录失败,请检查邮箱和密码是否正确")
                return
            except Exception as e:
                print(f"【错误】邮箱登录时发生未知错误: {str(e)}")
                return

            try:
                smtp.sendmail(config.email_sender, [config.email_receiver], message.as_string())
                print(f"\n【邮件通知】邮件发送成功!")
                print(f"- 发件人: {config.email_sender}")
                print(f"- 收件人: {config.email_receiver}")
                print(f"- 主题: {subject_line}")
            except Exception as e:
                print(f"【错误】邮件发送失败: {str(e)}")
            finally:
                smtp.quit()
        except Exception as e:
            print(f"【错误】SMTP连接失败: {str(e)}")

    except Exception as e:
        print(f"\n【错误】邮件处理过程出错: {str(e)}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")

def print_final_results(results_dict):
    """
    打印每个受试者的最终预测结果
    """
    print("\n" + "="*80)
    print("【训练最终结果】")
    print("="*80)
    
    if not results_dict or 'subject_results' not in results_dict:
        print("没有可用的训练结果")
        return
        
    print("\n【各受试者预测结果】")
    print("-"*50)
    
    for subject_id, data in sorted(results_dict['subject_results'].items()):
        print(f"\n受试者 {subject_id}:")
        print(f"性能指标: UF1={data['UF1']:.4f}, UAR={data['UAR']:.4f}")
        print("标签对比:")
        print(f"预测标签: {data['pred']}")
        print(f"真实标签: {data['truth']}")
        
        # 添加预测与真实标签的详细对比
        print("\n预测与真实标签的详细对比:")
        print(f"{'索引':^6}|{'预测':^6}|{'真实':^6}|{'是否正确':^8}")
        print("-"*30)
        correct_count = 0
        for i, (pred, truth) in enumerate(zip(data['pred'], data['truth'])):
            is_correct = pred == truth
            if is_correct:
                correct_count += 1
            print(f"{i:^6}|{pred:^6}|{truth:^6}|{'✓' if is_correct else '✗':^8}")
        
        accuracy = correct_count / len(data['pred']) if len(data['pred']) > 0 else 0
        print(f"\n准确率: {accuracy:.4f} ({correct_count}/{len(data['pred'])})")
        print("-"*50)
    
    if 'UF1' in results_dict and 'UAR' in results_dict:
        print(f"\n【总体性能】")
        print(f"UF1: {results_dict['UF1']:.4f}")
        print(f"UAR: {results_dict['UAR']:.4f}")
    
    if 'emotion_acc' in results_dict:
        print("\n【各表情类别准确率】")
        for emotion, acc in results_dict['emotion_acc'].items():
            print(f"- {emotion}: {acc:.4f}")
    
    print("\n" + "="*80)

if __name__ == '__main__':
    # 创建参数解析器
    parser = argparse.ArgumentParser(description='微表情识别模型训练参数配置')
    
    # 添加训练相关的命令行参数
    parser.add_argument('--train', type=strtobool, default=True, help='是否进行训练')
    parser.add_argument('--pre_trained', type=strtobool, default=True, help='是否使用预训练模型')
    parser.add_argument('--Aug_COCO_pre_trained', type=strtobool, default=True, help='是否使用COCO预训练的增强模型')
    parser.add_argument('--save_model', type=strtobool, default=True, help='是否保存模型')
    
    # 添加路径相关的参数/home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/SKD-TSTSAN_CK_plus.pth
    parser.add_argument('--pre_trained_model_path', type=str, default="/home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth", help="宏表情数据集预训练模型权重的路径")
    parser.add_argument('--main_path', type=str, default="./SAMM_LOSO_full", help="数据集主目录路径")
    parser.add_argument('--exp_name', type=str, help="保存实验结果的文件夹名称")
    
    # 添加训练超参数
    parser.add_argument('--learning_rate', type=float, default=0.001, help='学习率')
    parser.add_argument('--batch_size', type=int, default=32, help='基础批次大小(多GPU时会自动按GPU数量倍增)')
    parser.add_argument('--seed', type=int, default=1337, help='随机种子')
    parser.add_argument('--max_iter', type=int, default=20000, help='最大迭代次数')

    # 添加早停相关参数
    parser.add_argument('--use_early_stopping', type=strtobool, default=True,
                       help='🛑 是否启用早停机制(防止过拟合,提高训练效率)')
    parser.add_argument('--early_stopping_patience', type=int, default=200,
                       help='早停耐心值(连续多少轮验证准确率不提升就停止训练)')
    parser.add_argument('--early_stopping_min_delta', type=float, default=0.001,
                       help='早停最小改善阈值(准确率提升小于此值视为无改善)')
    parser.add_argument('--early_stopping_metric', type=str, default='accuracy',
                       choices=['accuracy', 'loss', 'f1_score'],
                       help='早停监控指标: accuracy-准确率(推荐), loss-损失值, f1_score-F1分数')
    parser.add_argument('--early_stopping_mode', type=str, default='max',
                       choices=['max', 'min'],
                       help='早停模式: max-指标越大越好(accuracy,f1), min-指标越小越好(loss)')
    parser.add_argument('--save_best_model', type=strtobool, default=True,
                       help='是否保存早停过程中的最佳模型(基于监控指标)')

    # 添加模型相关参数
    parser.add_argument('--model', type=str, default="SKD_TSTSAN", help='模型名称')
    parser.add_argument('--loss_function', type=str, default="FocalLoss_weighted", help='损失函数类型')
    parser.add_argument('--class_num', type=int, default=5, help='分类类别数')

    # 添加CUDNN相关参数
    parser.add_argument('--cudnn_benchmark', type=strtobool, default=False,
                       help='是否启用cudnn.benchmark(可能提升训练速度,但会增加结果随机性)')
    parser.add_argument('--cudnn_deterministic', type=strtobool, default=True,
                       help='是否启用cudnn.deterministic(保证结果可重复,但可能降低训练速度)')

    # 添加混合精度训练和TF32加速参数
    parser.add_argument('--use_mixed_precision', type=strtobool, default=False,
                       help='是否启用混合精度训练(FP16+FP32,可显著提升训练速度并减少显存使用)')
    parser.add_argument('--use_tf32', type=strtobool, default=True,
                       help='是否启用TF32加速计算(适用于Ampere架构GPU,如A100/RTX30系列)')

    # 添加BatchNorm修复参数
    parser.add_argument('--use_batchnorm_fix', type=strtobool, default=False,
                       help='是否启用BatchNorm修复(解决小批次训练时BatchNorm不稳定问题,适用于极小样本受试者)')

    # 添加卷积类型选择参数
    parser.add_argument('--conv_type', type=int, default=6, choices=[1, 2, 3, 4, 5, 6],
                       help='卷积类型选择: 1-标准卷积, 2-风车形卷积, 3-小波变换卷积, 4-WTRFAConv, 5-WTPRFAConv, 6-WTPRFAConv轻量化(推荐)')

    # 添加小波变换相关参数
    parser.add_argument('--wt_type', type=str, default='db4',
                       choices=['db1', 'db2', 'db4', 'db6', 'db8', 'haar', 'bior2.2', 'bior4.4', 'coif2', 'coif4', 'sym4', 'sym6'],
                       help='小波类型选择: db4(推荐), db6, bior2.2, bior4.4, coif2, sym4, sym6等')
    parser.add_argument('--wt_levels', type=int, default=1, choices=[1, 2, 3],
                       help='小波变换层数: 1(推荐), 2, 3')

    # 添加ECA注意力机制选择参数
    parser.add_argument('--eca_type', type=int, default=1, choices=[1, 2],
                       help='ECA注意力机制选择: 1-标准ECA(推荐), 2-增强型ECA')

    # 添加激活函数选择参数
    parser.add_argument('--activation_type', type=int, default=1, choices=[1, 2, 3],
                       help='激活函数选择: 1-标准ReLU, 2-只使用浅层Mish, 3-浅层Mish+深层FReLU(推荐)')

    # 添加跨分支交互选择参数
    parser.add_argument('--cross_branch_type', type=int, default=1, choices=[1, 2],
                       help='跨分支交互选择: 1-不使用跨分支交互, 2-启用动态跨分支特征交互(DCBF)')
    parser.add_argument('--use_cross_branch_fusion', type=bool, default=False,
                       help='是否启用跨分支交互功能')

    # 添加三模态特征融合选择参数
    parser.add_argument('--use_trimodal_fusion', type=strtobool, default=False,
                       help='🔥 是否启用先进的三模态特征融合模块(替代简单拼接,显著提升性能)')
    parser.add_argument('--trimodal_fusion_type', type=str, default='ensemble',
                       choices=['attention', 'gated', 'high_order', 'ensemble'],
                       help='三模态融合类型: attention-多头注意力融合, gated-门控融合, high_order-高阶交互, ensemble-集成融合(推荐)')
    parser.add_argument('--trimodal_num_heads', type=int, default=8, choices=[4, 8, 16],
                       help='多头注意力融合的头数(仅在fusion_type=attention时有效)')

    # 添加可学习时间移位模块选择参数
    parser.add_argument('--learnable_tsm_type', type=int, default=1, choices=[1, 2],
                       help='时间移位模块选择: 1-传统TSM, 2-可学习TSM(LTSM)')
    parser.add_argument('--use_learnable_tsm', type=bool, default=False,
                       help='是否启用可学习时间移位模块')

    # 添加知识蒸馏相关参数
    parser.add_argument('--temperature', default=3, type=int, help='知识蒸馏温度参数')
    parser.add_argument('--alpha', default=0.1, type=float, help='知识蒸馏损失权重')
    parser.add_argument('--beta', default=1e-6, type=float, help='特征损失权重')

    # 添加数据增强参数
    parser.add_argument('--Aug_alpha', type=float, default=2, help='数据增强强度参数')
    
    # 添加数据扩充参数
    parser.add_argument('--use_data_augmentation', type=strtobool, default=True, help='是否使用训练数据扩充')
    parser.add_argument('--use_test_augmentation', type=strtobool, default=True, help='是否对测试数据进行扩充')

    parser.add_argument('--test_aug_multipliers', type=str, default='7,2,10', help='测试数据各标签扩充倍数(逗号分隔) - SAMM 3分类: positive,negative,surprise')
    parser.add_argument('--voting_method', type=str, default='weighted', choices=['majority', 'weighted'], help='预测结果投票方式')
    parser.add_argument('--aug_rotation_range', type=str, default='3,8', help='旋转角度范围(min,max)')
    parser.add_argument('--aug_multipliers', type=str, default='7,2,10', help='训练数据各标签扩充倍数(逗号分隔) - SAMM 3分类: positive,negative,surprise')
    parser.add_argument('--aug_use_mirror', type=strtobool, default=True, help='是否使用镜像扩充')

    # 添加测试数据镜像训练参数
    parser.add_argument('--use_test_mirror_training', type=strtobool, default=True,
                       help='是否对特定受试者(017,014)使用测试数据的镜像进行额外训练(光流数据会正确处理)')
    parser.add_argument('--test_mirror_subjects', type=str, default='017,014,007,020,006',
                       help='需要使用测试数据镜像训练的受试者列表(逗号分隔,如017,014)')
    
    # 添加GPU数据预加载参数
    parser.add_argument('--use_gpu_preload', type=strtobool, default=True,
                       help='🚀 是否启用GPU数据预加载功能(将所有数据预先加载到GPU内存,显著提升训练速度)')

    # 添加并行训练参数
    parser.add_argument('--use_parallel_training', type=strtobool, default=False,
                       help='🚀 是否启用并行训练功能(多个受试者同时训练,显著缩短总训练时间)')
    parser.add_argument('--max_parallel_workers', type=int, default=2, choices=[1, 2, 3, 4],
                       help='并行训练的最大工作线程数(1-4,建议根据GPU数量和内存大小设置)')
    parser.add_argument('--single_subject', type=str, default=None,
                       help='单个受试者训练模式(指定受试者ID,如011,用于并行训练中的单个任务)')

    # 添加数据集参数
    parser.add_argument('--dataset', type=str, default="SAMM", help='数据集名称')

    # 添加Visdom相关参数
    parser.add_argument('--visdom_port', type=int, default=8097, help='Visdom服务器端口')
    parser.add_argument('--use_visdom', type=strtobool, default=False, help='是否使用Visdom可视化')
    parser.add_argument('--visdom_timeout', type=int, default=60, help='Visdom服务器启动超时时间(秒)')
    parser.add_argument('--open_browser', type=strtobool, default=False, help='是否自动打开浏览器')

    # 添加邮件通知相关参数
    parser.add_argument('--email_notify', type=strtobool, default=True, help='是否发送邮件通知')
    parser.add_argument('--email_sender', type=str, default="<EMAIL>", help='发件人邮箱')
    parser.add_argument('--email_password', type=str, default="njnqnnamaddpbdhb", help='发件人邮箱密码/授权码')
    parser.add_argument('--email_receiver', type=str, default="<EMAIL>", help='收件人邮箱')
    parser.add_argument('--smtp_server', type=str, default="smtp.qq.com", help='SMTP服务器')
    parser.add_argument('--smtp_port', type=int, default=587, help='SMTP端口')

    # 添加loss输出控制参数
    parser.add_argument('--show_loss_details', type=strtobool, default=True,
                       help='是否显示详细的loss信息(包括训练过程中的loss监控、各组件loss统计等)')
    parser.add_argument('--show_batch_loss', type=strtobool, default=False,
                       help='是否显示每个batch的详细loss信息(用于调试，会产生大量输出)')
    parser.add_argument('--show_epoch_loss', type=strtobool, default=True,
                       help='是否显示每个epoch结束时的loss统计信息')
    parser.add_argument('--show_validation_loss', type=strtobool, default=True,
                       help='是否显示验证过程中的loss信息')
    parser.add_argument('--show_overfitting_monitor', type=strtobool, default=True,
                       help='是否显示过拟合监控信息(训练vs验证loss比较)')

    # 解析命令行参数
    config = parser.parse_args()
    
    # 设置CUDNN行为和性能优化
    if torch.cuda.is_available():
        torch.backends.cudnn.benchmark = config.cudnn_benchmark
        torch.backends.cudnn.deterministic = config.cudnn_deterministic

        # 设置TF32加速
        if config.use_tf32:
            # 检查GPU是否支持TF32
            gpu_name = torch.cuda.get_device_name(0).lower()
            if 'a800' in gpu_name or 'rtx 30' in gpu_name or 'rtx 40' in gpu_name or 'ampere' in gpu_name:
                torch.backends.cuda.matmul.allow_tf32 = True
                torch.backends.cudnn.allow_tf32 = True
                print_success("TF32加速已启用 (检测到支持的GPU架构)", "🚀")
            else:
                print_warning(f"当前GPU ({torch.cuda.get_device_name(0)}) 可能不支持TF32加速")
                print_info("TF32主要适用于Ampere架构GPU (A100, RTX 30/40系列)")
                torch.backends.cuda.matmul.allow_tf32 = config.use_tf32
                torch.backends.cudnn.allow_tf32 = config.use_tf32
        else:
            torch.backends.cuda.matmul.allow_tf32 = False
            torch.backends.cudnn.allow_tf32 = False

        print_section("GPU性能配置", Colors.BRIGHT_MAGENTA, "🎮")
        print_info(f"GPU设备: {torch.cuda.get_device_name(0)}", "🖥️")

        # 使用颜色显示配置状态
        benchmark_status = colorize("启用", Colors.BRIGHT_GREEN) if config.cudnn_benchmark else colorize("禁用", Colors.BRIGHT_RED)
        deterministic_status = colorize("启用", Colors.BRIGHT_GREEN) if config.cudnn_deterministic else colorize("禁用", Colors.BRIGHT_RED)
        mixed_precision_status = colorize("启用", Colors.BRIGHT_GREEN) if config.use_mixed_precision else colorize("禁用", Colors.BRIGHT_RED)
        tf32_status = colorize("启用", Colors.BRIGHT_GREEN) if config.use_tf32 else colorize("禁用", Colors.BRIGHT_RED)

        print(f"⚡ CUDNN Benchmark: {benchmark_status}")
        print(f"🔒 CUDNN Deterministic: {deterministic_status}")
        print(f"🎯 混合精度训练: {mixed_precision_status}")
        print(f"🚀 TF32加速: {tf32_status}")

        if config.cudnn_benchmark:
            print_warning("启用cudnn.benchmark可能会影响结果的可重复性")
        if config.use_mixed_precision:
            print_highlight("混合精度训练可显著提升训练速度并减少显存使用", "💡")
        if config.use_tf32:
            print_highlight("TF32加速适用于Ampere架构GPU,可提升计算性能", "⚡")

    # 在开始训练前询问实验名称
    print_banner("欢迎使用微表情识别模型训练系统", Colors.BRIGHT_CYAN, "🎉")
    print("\n请输入本次实验的主题名称（将用作实验结果文件夹名称）：")
    exp_name = input().strip()

    # 如果用户没有输入，使用默认值
    if not exp_name:
        exp_name = "SKD_TSTSAN_SAMM_experiment"
        print_info(f"未输入实验名称，将使用默认名称：{exp_name}", "📝")
    else:
        print_success(f"实验名称已设置为：{exp_name}", "📝")

    # SAMM数据集只有3分类，直接设置参数
    print_section("分类方案选择", Colors.BRIGHT_GREEN, "🎯")
    print(f"{colorize('SAMM数据集分类方案：', Colors.BOLD + Colors.BRIGHT_CYAN)}")
    print(f"{colorize('✅ 3分类', Colors.BRIGHT_GREEN)} {colorize('(正性、负性、惊讶)', Colors.CYAN)} 😊😮😲")
    print(f"{colorize('📊 样本统计:', Colors.BOLD + Colors.BRIGHT_BLUE)}")
    print(f"   • positive: 26 个样本")
    print(f"   • negative: 90 个样本")
    print(f"   • surprise: 15 个样本")
    print(f"   • 总样本数: 131 个样本")
    print(f"{colorize('🔢 增强倍数: 7,2,10', Colors.BOLD + Colors.BRIGHT_MAGENTA)}")

    # 设置SAMM数据集的固定参数
    parser.set_defaults(class_num=3)  # SAMM只有3分类
    parser.set_defaults(aug_multipliers="7,2,10")  # 根据您提供的增强倍数
    parser.set_defaults(test_aug_multipliers="7,2,10")
    print_success("已选择3分类方案", "🎯")

    # 根据分类方案调整实验名称
    if exp_name == "SKD_TSTSAN_SAMM_experiment":
        exp_name = "SKD_TSTSAN_SAMM_class3_full"
    elif not exp_name.endswith("_class3"):
        exp_name += "_class3"

    # 询问用户选择卷积类型
    selector = ConvolutionSelector()
    print_section("卷积类型选择", Colors.BRIGHT_BLUE, "🔧")
    print(f"{colorize('🚀 选择您的AI引擎：', Colors.BOLD + Colors.BRIGHT_CYAN)}")
    selector.display_options()

    while True:
        try:
            conv_choice = input(f"{colorize('请选择卷积类型 (1-6): ', Colors.BOLD + Colors.BRIGHT_MAGENTA)}").strip()
            conv_choice = int(conv_choice)

            if conv_choice in range(1, 7):
                parser.set_defaults(conv_type=conv_choice)
                conv_name = selector.get_conv_name(conv_choice)
                print_success(f"已选择: {conv_name}", "🎉")

                # 如果选择了小波相关的卷积类型，提供小波选择界面
                if conv_choice in [3, 4, 5, 6]:  # 小波相关的卷积类型
                    print_section("小波类型选择", Colors.BRIGHT_CYAN, "🌊")
                    print(f"{colorize('🎯 您选择了小波相关的卷积类型，请选择具体的小波类型：', Colors.BOLD + Colors.BRIGHT_CYAN)}")

                    # 显示小波选择选项
                    wavelet_options = {
                        1: {"type": "bior2.2", "desc": "双正交2.2小波 - 对称性好，适合面部特征 (强烈推荐)"},
                        2: {"type": "db6", "desc": "6阶Daubechies小波 - 更好频率分辨率，适合复杂模式"},
                        3: {"type": "sym4", "desc": "Symlets4小波 - 近似对称，适合面部特征分析"},
                        4: {"type": "db4", "desc": "4阶Daubechies小波 - 平衡时频分辨率 (默认)"},
                        5: {"type": "coif2", "desc": "Coiflets2小波 - 紧支撑特性，适合边缘特征"},
                        6: {"type": "sym6", "desc": "Symlets6小波 - 更好对称性，适合模式识别"},
                        7: {"type": "bior4.4", "desc": "双正交4.4小波 - 平滑重构，适合时序分析"},
                        8: {"type": "haar", "desc": "Haar小波 - 计算最快，适合实时应用"}
                    }

                    print(f"\n{colorize('⭐ 推荐小波类型:', Colors.BOLD + Colors.BRIGHT_GREEN)}")
                    for i in [1, 2, 3]:  # 显示前3个推荐选项
                        opt = wavelet_options[i]
                        print(f"{i}. {colorize(opt['type'], Colors.BOLD):8s} - {opt['desc']}")

                    print(f"\n{colorize('📋 其他可选小波:', Colors.BOLD + Colors.BRIGHT_YELLOW)}")
                    for i in [4, 5, 6, 7, 8]:  # 显示其他选项
                        opt = wavelet_options[i]
                        print(f"{i}. {colorize(opt['type'], Colors.BOLD):8s} - {opt['desc']}")

                    print(f"\n{colorize('💡 建议:', Colors.BOLD + Colors.BRIGHT_BLUE)}")
                    print("• 首次使用: 推荐选择 1 (bior2.2)")
                    print("• 性能优化: 可尝试 2 (db6) 或 3 (sym4)")
                    print("• 快速测试: 可选择 8 (haar)")

                    while True:
                        try:
                            wt_choice = input(f"{colorize('请选择小波类型 (1-8): ', Colors.BOLD + Colors.BRIGHT_MAGENTA)}").strip()
                            wt_choice = int(wt_choice)
                            if 1 <= wt_choice <= 8:
                                selected_wavelet = wavelet_options[wt_choice]
                                print_success(f"已选择小波: {selected_wavelet['type']} - {selected_wavelet['desc']}", "🌊")
                                parser.set_defaults(wt_type=selected_wavelet['type'])
                                break
                            else:
                                print_error("请输入1-8之间的数字")
                        except ValueError:
                            print_error("请输入有效的数字")

                    # 选择小波变换层数
                    print(f"\n{colorize('🔢 小波变换层数选择:', Colors.BOLD + Colors.BRIGHT_CYAN)}")
                    print("1. 单层变换 (wt_levels=1) - 推荐，计算快速")
                    print("2. 双层变换 (wt_levels=2) - 更深分解，可能更好效果")

                    while True:
                        try:
                            levels_choice = input(f"{colorize('请选择变换层数 (1-2): ', Colors.BOLD + Colors.BRIGHT_MAGENTA)}").strip()
                            levels_choice = int(levels_choice)
                            if levels_choice in [1, 2]:
                                print_success(f"已选择: {levels_choice}层小波变换", "🔢")
                                parser.set_defaults(wt_levels=levels_choice)
                                break
                            else:
                                print_error("请输入1或2")
                        except ValueError:
                            print_error("请输入有效的数字")

                # 根据卷积类型调整实验名称
                if conv_choice in [3, 4, 5, 6]:  # 小波相关卷积
                    # 获取小波参数
                    wt_type = getattr(parser.parse_args(), 'wt_type', 'db4')
                    wt_levels = getattr(parser.parse_args(), 'wt_levels', 1)
                    conv_suffix = f"_conv{conv_choice}_{wt_type}_L{wt_levels}"
                else:
                    conv_suffix = f"_conv{conv_choice}"

                if not any(exp_name.endswith(f"_conv{i}") for i in range(1, 7)):
                    exp_name += conv_suffix
                else:
                    # 替换现有的卷积后缀
                    for i in range(1, 7):
                        if exp_name.endswith(f"_conv{i}") or any(f"_conv{i}_" in exp_name for i in range(1, 7)):
                            # 找到并替换包含小波信息的卷积后缀
                            import re
                            pattern = r'_conv\d+(_\w+_L\d+)?'
                            exp_name = re.sub(pattern, conv_suffix, exp_name)
                            break
                break
            else:
                print_error("无效选择，请输入1-6之间的数字")
        except ValueError:
            print_error("请输入有效的数字")

    # 询问用户选择ECA注意力机制类型
    eca_selector = ECASelector()
    print_section("ECA注意力机制选择", Colors.BRIGHT_RED, "🔥")
    print(f"{colorize('🎯 选择您的注意力增强引擎：', Colors.BOLD + Colors.BRIGHT_CYAN)}")
    eca_selector.display_options()

    while True:
        try:
            eca_choice = input(f"{colorize('请选择ECA注意力机制类型 (1-2): ', Colors.BOLD + Colors.BRIGHT_RED)}").strip()
            eca_choice = int(eca_choice)

            if eca_choice in range(1, 3):
                parser.set_defaults(eca_type=eca_choice)
                eca_name = eca_selector.get_eca_name(eca_choice)
                eca_emoji = eca_selector.get_eca_emoji(eca_choice)
                print_success(f"已选择: {eca_emoji} {eca_name}", "🎉")

                # 根据ECA类型调整实验名称
                eca_suffix = f"_eca{eca_choice}"
                if not any(exp_name.endswith(f"_eca{i}") for i in range(1, 3)):
                    exp_name += eca_suffix
                else:
                    # 替换现有的ECA后缀
                    for i in range(1, 3):
                        if exp_name.endswith(f"_eca{i}"):
                            exp_name = exp_name.replace(f"_eca{i}", eca_suffix)
                            break
                break
            else:
                print_error("无效选择，请输入1-2之间的数字")
        except ValueError:
            print_error("请输入有效的数字")

    # 询问用户选择激活函数类型
    activation_selector = ActivationSelector()
    print_section("激活函数选择", Colors.BRIGHT_YELLOW, "🎯")
    print(f"{colorize('🔥 选择您的激活函数组合策略：', Colors.BOLD + Colors.BRIGHT_CYAN)}")
    activation_selector.display_options()

    while True:
        try:
            activation_choice = input(f"{colorize('请选择激活函数类型 (1-3): ', Colors.BOLD + Colors.BRIGHT_YELLOW)}").strip()
            activation_choice = int(activation_choice)

            if activation_choice in range(1, 4):
                parser.set_defaults(activation_type=activation_choice)
                activation_name = activation_selector.get_activation_name(activation_choice)
                activation_emoji = activation_selector.get_activation_emoji(activation_choice)
                print_success(f"已选择: {activation_emoji} {activation_name}", "🎉")

                # 根据激活函数类型调整实验名称
                activation_suffix = activation_selector.get_experiment_suffix(activation_choice)
                if not any(exp_name.endswith(f"_{suffix}") for suffix in ["relu", "mish_only", "mish_frelu"]):
                    exp_name += activation_suffix
                else:
                    # 替换现有的激活函数后缀
                    for suffix in ["relu", "mish_only", "mish_frelu"]:
                        if exp_name.endswith(f"_{suffix}"):
                            exp_name = exp_name.replace(f"_{suffix}", activation_suffix)
                            break
                break
            else:
                print_error("无效选择，请输入1-3之间的数字")
        except ValueError:
            print_error("请输入有效的数字")

    # 询问用户选择跨分支交互类型
    cross_branch_selector = CrossBranchSelector()
    print_section("跨分支交互机制选择", Colors.BRIGHT_CYAN, "🔗")
    print(f"{colorize('🚀 选择您的分支交互引擎：', Colors.BOLD + Colors.BRIGHT_CYAN)}")
    cross_branch_selector.display_options()

    while True:
        try:
            cross_branch_choice = input(f"{colorize('请选择跨分支交互类型 (1-2): ', Colors.BOLD + Colors.BRIGHT_CYAN)}").strip()
            cross_branch_choice = int(cross_branch_choice)

            if cross_branch_choice in range(1, 3):
                parser.set_defaults(cross_branch_type=cross_branch_choice)
                cross_branch_name = cross_branch_selector.get_cross_branch_name(cross_branch_choice)
                cross_branch_emoji = cross_branch_selector.get_cross_branch_emoji(cross_branch_choice)
                print_success(f"已选择: {cross_branch_emoji} {cross_branch_name}", "🎉")

                # 根据跨分支交互类型调整实验名称和配置
                if cross_branch_choice == 2:  # 启用跨分支交互
                    parser.set_defaults(use_cross_branch_fusion=True)
                    cross_branch_suffix = "_dcbf"
                    if not exp_name.endswith("_dcbf"):
                        exp_name += cross_branch_suffix
                else:  # 不启用跨分支交互
                    parser.set_defaults(use_cross_branch_fusion=False)
                    # 移除可能存在的dcbf后缀
                    if exp_name.endswith("_dcbf"):
                        exp_name = exp_name.replace("_dcbf", "")

                break
            else:
                print_error("无效选择，请输入1-2之间的数字")
        except ValueError:
            print_error("请输入有效的数字")

    # 询问用户选择可学习时间移位模块类型
    tsm_selector = LearnableTSMSelector()
    print_section("时间移位模块选择", Colors.BRIGHT_MAGENTA, "🔄")
    print(f"{colorize('🚀 选择您的时间建模引擎：', Colors.BOLD + Colors.BRIGHT_CYAN)}")
    tsm_selector.display_options()

    while True:
        try:
            tsm_choice = input(f"{colorize('请选择时间移位模块类型 (1-2): ', Colors.BOLD + Colors.BRIGHT_MAGENTA)}").strip()
            tsm_choice = int(tsm_choice)

            if tsm_choice in range(1, 3):
                parser.set_defaults(learnable_tsm_type=tsm_choice)
                tsm_name = tsm_selector.get_tsm_name(tsm_choice)
                tsm_emoji = tsm_selector.get_tsm_emoji(tsm_choice)
                print_success(f"已选择: {tsm_emoji} {tsm_name}", "🎉")

                # 根据TSM类型调整实验名称和配置
                if tsm_choice == 2:  # 启用可学习TSM
                    parser.set_defaults(use_learnable_tsm=True)
                    tsm_suffix = "_ltsm"
                    if not exp_name.endswith("_ltsm"):
                        exp_name += tsm_suffix
                else:  # 使用传统TSM
                    parser.set_defaults(use_learnable_tsm=False)
                    # 移除可能存在的ltsm后缀
                    if exp_name.endswith("_ltsm"):
                        exp_name = exp_name.replace("_ltsm", "")

                break
            else:
                print_error("无效选择，请输入1-2之间的数字")
        except ValueError:
            print_error("请输入有效的数字")

    # 询问用户选择三模态特征融合类型
    fusion_selector = TriModalFusionSelector()
    print_section("三模态特征融合选择", Colors.BRIGHT_MAGENTA, "🔗")
    print(f"{colorize('🚀 选择您的特征融合引擎：', Colors.BOLD + Colors.BRIGHT_CYAN)}")
    fusion_selector.display_options()

    while True:
        try:
            fusion_choice = input(f"{colorize('请选择融合方法 (1-5): ', Colors.BOLD + Colors.BRIGHT_MAGENTA)}").strip()
            fusion_choice = int(fusion_choice)

            if fusion_choice in range(1, 6):
                fusion_config = fusion_selector.get_fusion_config(fusion_choice)
                fusion_name, fusion_emoji = fusion_selector.get_fusion_name(fusion_choice)

                # 设置融合相关参数
                parser.set_defaults(
                    use_trimodal_fusion=fusion_config['use_trimodal_fusion'],
                    trimodal_fusion_type=fusion_config['trimodal_fusion_type'],
                    trimodal_num_heads=fusion_config['trimodal_num_heads']
                )

                print_success(f"已选择: {fusion_emoji} {fusion_name}", "🎉")

                # 获取融合类型后缀
                fusion_suffix = fusion_config['suffix']

                # 如果选择了多头注意力融合，询问头数
                if fusion_choice == 2:  # 多头注意力融合
                    print_section("多头注意力头数选择", Colors.BRIGHT_BLUE, "🧠")
                    print(f"{colorize('🎯 您选择了多头注意力融合，请选择注意力头数：', Colors.BOLD + Colors.BRIGHT_CYAN)}")

                    head_options = {
                        1: {'heads': 4, 'name': '4头注意力', 'description': '轻量级，计算快速', 'emoji': '⚡'},
                        2: {'heads': 8, 'name': '8头注意力', 'description': '平衡性能与效率，推荐选择', 'emoji': '⭐'},
                        3: {'heads': 16, 'name': '16头注意力', 'description': '最强表达能力，计算开销大', 'emoji': '🔥'}
                    }

                    print("\n" + "="*60)
                    for option_id, option in head_options.items():
                        option_text = f"{option_id}. {option['emoji']} {option['name']}"
                        print(f"{colorize(option_text, Colors.BRIGHT_YELLOW)}")
                        print(f"   📝 {option['description']}")
                    print("="*60)

                    while True:
                        try:
                            head_choice = input(f"{colorize('请选择注意力头数 (1-3): ', Colors.BOLD + Colors.BRIGHT_BLUE)}").strip()
                            head_choice = int(head_choice)

                            if head_choice in range(1, 4):
                                selected_heads = head_options[head_choice]['heads']
                                head_name = head_options[head_choice]['name']
                                head_emoji = head_options[head_choice]['emoji']

                                # 更新头数配置
                                parser.set_defaults(trimodal_num_heads=selected_heads)

                                print_success(f"已选择: {head_emoji} {head_name} ({selected_heads}头)", "🎉")

                                # 根据头数调整实验名称后缀
                                if fusion_suffix:
                                    fusion_suffix += f"_h{selected_heads}"

                                break
                            else:
                                print_error("无效选择，请输入1-3之间的数字")
                        except ValueError:
                            print_error("请输入有效的数字")

                # 根据融合类型调整实验名称
                if fusion_suffix and not exp_name.endswith(fusion_suffix):
                    exp_name += fusion_suffix

                break
            else:
                print_error("无效选择，请输入1-5之间的数字")
        except ValueError:
            print_error("请输入有效的数字")

    # 询问用户选择损失输出级别
    print_section("损失输出级别选择", Colors.BRIGHT_YELLOW, "📊")
    print(f"{colorize('🚀 选择您的损失信息输出级别：', Colors.BOLD + Colors.BRIGHT_CYAN)}")

    print("\n" + "="*80)
    print(f"{colorize('📊 损失输出级别选择器', Colors.BOLD + Colors.BRIGHT_CYAN)}")
    print("="*80)

    loss_output_options = {
        1: {
            'name': '静默模式',
            'emoji': '🔇',
            'description': '不显示详细损失信息，仅显示基本训练进度',
            'features': [
                '仅显示准确率和基本进度',
                '最简洁的输出',
                '适合后台运行',
                '减少终端输出量'
            ],
            'config': {
                'show_loss_details': False,
                'show_epoch_loss': False,
                'show_validation_loss': False,
                'show_overfitting_monitor': False,
                'show_batch_loss': False
            }
        },
        2: {
            'name': '基础模式',
            'emoji': '📈',
            'description': '显示基本损失信息，适合日常训练监控',
            'features': [
                '显示主要损失值',
                '显示训练和验证准确率',
                '基本的训练监控',
                '平衡的信息量'
            ],
            'config': {
                'show_loss_details': True,
                'show_epoch_loss': True,
                'show_validation_loss': True,
                'show_overfitting_monitor': False,
                'show_batch_loss': False
            }
        },
        3: {
            'name': '详细模式 (推荐)',
            'emoji': '📊',
            'description': '显示完整的损失统计和分析，推荐用于性能调优',
            'features': [
                '完整的损失函数统计',
                '损失组成分析',
                '过拟合监控',
                '详细的性能分析'
            ],
            'config': {
                'show_loss_details': True,
                'show_epoch_loss': True,
                'show_validation_loss': True,
                'show_overfitting_monitor': True,
                'show_batch_loss': False
            }
        },
        4: {
            'name': '调试模式',
            'emoji': '🔍',
            'description': '显示所有可能的损失信息，用于深度调试',
            'features': [
                '批次级别的损失详情',
                '完整的损失分解',
                '详细的过拟合分析',
                '最全面的调试信息'
            ],
            'config': {
                'show_loss_details': True,
                'show_epoch_loss': True,
                'show_validation_loss': True,
                'show_overfitting_monitor': True,
                'show_batch_loss': True
            }
        }
    }

    for option_id, option in loss_output_options.items():
        option_title = f"{option_id}. {option['emoji']} {option['name']}"
        print(f"\n{colorize(option_title, Colors.BRIGHT_YELLOW)}")
        print(f"   📝 描述: {option['description']}")
        print(f"   🔍 特征:")
        for feature in option['features']:
            print(f"      • {feature}")

    print("\n" + "="*80)
    print(f"{colorize('💡 推荐说明:', Colors.BRIGHT_GREEN)}")
    print(f"   🔇 选择1: 静默运行，最少输出")
    print(f"   📈 选择2: 日常训练，基本监控")
    print(f"   📊 选择3: 性能调优，详细分析 ⭐⭐ 推荐")
    print(f"   🔍 选择4: 深度调试，完整信息")
    print("="*80)

    while True:
        try:
            loss_choice = input(f"{colorize('请选择损失输出级别 (1-4): ', Colors.BOLD + Colors.BRIGHT_YELLOW)}").strip()
            loss_choice = int(loss_choice)

            if loss_choice in range(1, 5):
                loss_config = loss_output_options[loss_choice]['config']
                loss_name = loss_output_options[loss_choice]['name']
                loss_emoji = loss_output_options[loss_choice]['emoji']

                # 设置损失输出相关参数
                parser.set_defaults(**loss_config)

                print_success(f"已选择: {loss_emoji} {loss_name}", "🎉")

                # 显示配置详情
                print("\n📋 配置详情:")
                for key, value in loss_config.items():
                    status = "启用" if value else "禁用"
                    color = Colors.BRIGHT_GREEN if value else Colors.BRIGHT_RED
                    icon = "✅" if value else "❌"
                    print(f"   {icon} {key}: {colorize(status, color)}")

                break
            else:
                print_error("无效选择，请输入1-4之间的数字")
        except ValueError:
            print_error("请输入有效的数字")

    # 重新解析参数以应用新的默认值
    config = parser.parse_args()

    # 更新配置中的实验名称
    config.exp_name = exp_name
    print_highlight(f"最终实验名称：{exp_name}", "📝")

    # 预训练模型选择功能
    if config.pre_trained:
        print("\n" + "="*80)
        print("🤖 预训练模型配置")
        print("="*80)

        # 直接显示预训练模型选择界面
        print("请选择预训练模型:")
        selected_model = scan_and_select_pretrained_model()
        if selected_model:
            config.pre_trained_model_path = selected_model
            print(f"✅ 已更新预训练模型路径")
        else:
            print(f"✅ 使用默认预训练模型: {os.path.basename(config.pre_trained_model_path)}")

        print(f"📍 当前预训练模型: {colorize(os.path.basename(config.pre_trained_model_path), Colors.BRIGHT_CYAN)}")
    else:
        print_info("预训练模型功能已禁用", "🚫")
    
    # 打印配置信息
    print_section("训练配置信息", Colors.BRIGHT_GREEN, "⚙️")
    print(f"🏷️  {colorize('实验名称:', Colors.BOLD)} {colorize(config.exp_name, Colors.BRIGHT_CYAN)}")
    print(f"🤖 {colorize('模型名称:', Colors.BOLD)} {colorize(config.model, Colors.BRIGHT_BLUE)}")
    print(f"📊 {colorize('损失函数:', Colors.BOLD)} {colorize(config.loss_function, Colors.BRIGHT_MAGENTA)}")
    print(f"📈 {colorize('学习率:', Colors.BOLD)} {colorize(str(config.learning_rate), Colors.BRIGHT_YELLOW)}")
    print(f"📦 {colorize('基础批次大小:', Colors.BOLD)} {colorize(str(config.batch_size), Colors.BRIGHT_GREEN)}")
    print(f"🔄 {colorize('最大迭代次数:', Colors.BOLD)} {colorize(str(config.max_iter), Colors.BRIGHT_RED)}")
    print(f"🎯 {colorize('分类类别数:', Colors.BOLD)} {colorize(str(config.class_num), Colors.BRIGHT_CYAN)}")

    # 显示卷积、ECA、激活函数、跨分支交互、时间移位和特征融合配置
    conv_name = selector.get_conv_name(config.conv_type)
    eca_name = eca_selector.get_eca_name(config.eca_type)
    eca_emoji = eca_selector.get_eca_emoji(config.eca_type)
    activation_name = activation_selector.get_activation_name(config.activation_type)
    activation_emoji = activation_selector.get_activation_emoji(config.activation_type)
    cross_branch_name = cross_branch_selector.get_cross_branch_name(config.cross_branch_type)
    cross_branch_emoji = cross_branch_selector.get_cross_branch_emoji(config.cross_branch_type)
    tsm_name = tsm_selector.get_tsm_name(config.learnable_tsm_type)
    tsm_emoji = tsm_selector.get_tsm_emoji(config.learnable_tsm_type)

    # 获取三模态融合配置显示
    if hasattr(config, 'use_trimodal_fusion') and config.use_trimodal_fusion:
        fusion_type_map = {
            'attention': '🧠 多头注意力融合',
            'gated': '🚪 门控融合',
            'high_order': '🔬 高阶交互融合',
            'ensemble': '🏆 集成融合'
        }
        fusion_display = fusion_type_map.get(config.trimodal_fusion_type, '📝 传统特征拼接')
    else:
        fusion_display = '📝 传统特征拼接'

    print(f"🔧 {colorize('卷积类型:', Colors.BOLD)} {colorize(conv_name, Colors.BRIGHT_BLUE)}")
    print(f"🔥 {colorize('ECA注意力:', Colors.BOLD)} {colorize(f'{eca_emoji} {eca_name}', Colors.BRIGHT_RED)}")
    print(f"🎯 {colorize('激活函数:', Colors.BOLD)} {colorize(f'{activation_emoji} {activation_name}', Colors.BRIGHT_YELLOW)}")
    print(f"🔗 {colorize('跨分支交互:', Colors.BOLD)} {colorize(f'{cross_branch_emoji} {cross_branch_name}', Colors.BRIGHT_CYAN)}")
    print(f"🔄 {colorize('时间移位模块:', Colors.BOLD)} {colorize(f'{tsm_emoji} {tsm_name}', Colors.BRIGHT_MAGENTA)}")
    print(f"🔗 {colorize('特征融合方法:', Colors.BOLD)} {colorize(fusion_display, Colors.BRIGHT_GREEN)}")

    # 显示GPU配置信息
    print_section("GPU配置信息", Colors.BRIGHT_YELLOW, "🖥️")
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        if gpu_count > 1:
            effective_batch_size = config.batch_size * gpu_count
            print_success(f"多GPU训练 ({gpu_count}个GPU)", "🚀")
            print_info(f"实际批次大小: {effective_batch_size} (基础批次 × GPU数量)", "📊")
        else:
            print_info("单GPU训练", "🖥️")
            print_info(f"实际批次大小: {config.batch_size}", "📊")
    else:
        print_warning("CPU训练", "💻")
        print_info(f"实际批次大小: {config.batch_size}", "📊")

    # 显示分类方案详情
    print_section("分类方案", Colors.BRIGHT_MAGENTA, "🎭")
    if config.class_num == 3:
        print(f"🎯 {colorize('3分类方案', Colors.BRIGHT_GREEN)} {colorize('(正性、负性、惊讶)', Colors.CYAN)} 😊😮😲")
    else:
        print(f"🎯 {colorize('5分类方案', Colors.BRIGHT_GREEN)} {colorize('(快乐、惊讶、厌恶、压抑、其他)', Colors.CYAN)} 😊😮🤢😔❓")

    print_section("性能优化配置", Colors.BRIGHT_BLUE, "⚡")

    # 使用颜色显示各种配置状态
    def status_color(enabled):
        return colorize("启用", Colors.BRIGHT_GREEN) if enabled else colorize("禁用", Colors.BRIGHT_RED)

    print(f"🏃 CUDNN Benchmark模式: {status_color(config.cudnn_benchmark)}")
    print(f"🔒 CUDNN 确定性模式: {status_color(config.cudnn_deterministic)}")
    print(f"🎯 混合精度训练: {status_color(config.use_mixed_precision)}")
    print(f"🚀 TF32加速: {status_color(config.use_tf32)}")
    print(f"🔧 BatchNorm修复: {status_color(getattr(config, 'use_batchnorm_fix', False))}")
    print(f"🔧 卷积类型: {colorize(get_conv_type_name(config.conv_type), Colors.BRIGHT_CYAN)}")
    if config.use_data_augmentation:
        print_section("数据增强配置", Colors.BRIGHT_CYAN, "🔄")
        print(f"🔄 {colorize('旋转角度范围:', Colors.BOLD)} {colorize(config.aug_rotation_range, Colors.BRIGHT_YELLOW)}")
        print(f"🪞 {colorize('使用镜像:', Colors.BOLD)} {status_color(config.aug_use_mirror)}")
        print(f"📈 {colorize('训练数据扩充倍数:', Colors.BOLD)} {colorize(config.aug_multipliers, Colors.BRIGHT_GREEN)}")
        print(f"📊 {colorize('测试数据扩充倍数:', Colors.BOLD)} {colorize(config.test_aug_multipliers, Colors.BRIGHT_BLUE)}")
        if getattr(config, 'use_test_mirror_training', False):
            print(f"🔄 {colorize('测试数据镜像训练:', Colors.BOLD)} {colorize('启用', Colors.BRIGHT_GREEN)} {colorize(f'(受试者: {config.test_mirror_subjects})', Colors.CYAN)}")
        else:
            print(f"🔄 {colorize('测试数据镜像训练:', Colors.BOLD)} {colorize('禁用', Colors.BRIGHT_RED)}")

    # 启动训练
    print_banner("🚀 启动AI训练引擎 🚀", Colors.BRIGHT_GREEN, "🎯")
    print(f"{colorize('✨ 准备开始这段激动人心的AI训练之旅！ ✨', Colors.BOLD + Colors.BRIGHT_MAGENTA)}")
    start_time = time.time()
    results_dict = None
    logger = None
    
    try:
        # 先创建实验目录
        exp_dir = './Experiment_for_recognize/' + config.exp_name
        try:
            if not os.path.exists(exp_dir):
                os.makedirs(exp_dir, exist_ok=True)
                print(f'创建实验目录: {config.exp_name}')
            else:
                print(f'实验目录已存在: {config.exp_name}')
        except Exception as e:
            print(f'创建实验目录失败: {str(e)}')
            print(f'目录路径: {exp_dir}')
            raise

        # 设置日志文件
        log_file_path = os.path.join(exp_dir, "log.txt")
        print(f'准备创建日志文件: {log_file_path}')

        try:
            logger = Logger(log_file_path)
            sys.stdout = logger
            print(f'日志系统初始化成功')
        except Exception as e:
            print(f'日志系统初始化失败: {str(e)}')
            print(f'将继续运行但不记录日志文件')
            logger = None
        
        results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
        total_time = time.time() - start_time
        
        # 生成并打印实验报告
        sys_info = get_system_info()
        brief_results, detailed_results = format_subject_results(results_dict)
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print("\n" + "="*80)
        print("【实验结果报告】")
        print("="*80)
        
        print("\n【基本信息】")
        print(f"实验名称: {config.exp_name}")
        print(f"时间: {current_time}")
        print(f"总训练时间: {format_time(total_time)}")
        print(f"数据集: {os.path.basename(config.main_path)}")
        
        print("\n【系统环境】")
        print(f"操作系统: {sys_info['OS']}")
        print(f"处理器: {sys_info['CPU']}")
        print(f"内存: {sys_info['内存']}")
        print(f"GPU: {sys_info['GPU']}")
        
        print("\n【模型配置】")
        print(f"模型架构: {config.model}")
        print(f"是否使用预训练: {'是' if config.pre_trained else '否'}")
        print(f"预训练模型: {os.path.basename(config.pre_trained_model_path) if config.pre_trained else '无'}")
        print(f"是否使用COCO预训练: {'是' if config.Aug_COCO_pre_trained else '否'}")
        print(f"是否保存模型: {'是' if config.save_model else '否'}")
        
        print("\n【训练参数】")
        print(f"学习率: {config.learning_rate}")
        print(f"批次大小: {config.batch_size}")
        print(f"最大迭代次数: {config.max_iter}")
        print(f"损失函数: {config.loss_function}")
        print(f"随机种子: {config.seed}")

        print("\n【GPU性能配置】")
        print(f"CUDNN Benchmark: {'启用' if config.cudnn_benchmark else '禁用'}")
        print(f"CUDNN Deterministic: {'启用' if config.cudnn_deterministic else '禁用'}")
        print(f"混合精度训练: {'启用' if config.use_mixed_precision else '禁用'}")
        print(f"TF32加速: {'启用' if config.use_tf32 else '禁用'}")
        print(f"BatchNorm修复: {'启用' if getattr(config, 'use_batchnorm_fix', False) else '禁用'}")
        print(f"卷积类型: {get_conv_type_name(config.conv_type)}")
        
        print("\n【蒸馏参数】")
        print(f"温度: {config.temperature}")
        print(f"α值: {config.alpha}")
        print(f"β值: {config.beta}")
        print(f"数据增强系数: {config.Aug_alpha}")
        
        print("\n【训练结果】")
        if 'UF1' in results_dict and 'UAR' in results_dict:
            print(f"总体性能:")
            print(f"- UF1分数: {format_metric(results_dict['UF1'])}")
            print(f"- UAR分数: {format_metric(results_dict['UAR'])}")
        
        if 'emotion_acc' in results_dict:
            print("\n【各表情类别准确率】")
            print("-"*50)
            for emotion, acc in results_dict['emotion_acc'].items():
                print(f"- {emotion}: {format_metric(acc)}")
        
        # 打印每个受试者的结果
        print(brief_results)
        print(detailed_results)
        
        print("="*80)
        
        # 如果启用了邮件通知，发送训练结果
        if config.email_notify and config.email_sender and config.email_receiver:
            send_training_results(config, results_dict, total_time)
            
        print("\n训练完成!")
        
    except Exception as e:
        print(f"\n训练过程出错: {str(e)}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")

        # 如果启用了邮件通知，发送错误信息
        if config.email_notify and config.email_sender and config.email_receiver:
            try:
                error_results = {
                    'error': str(e),
                    'status': 'failed',
                    'subject_results': {},
                    'UF1': 0.0,
                    'UAR': 0.0,
                    'emotion_acc': {}
                }
                send_training_results(config, error_results, time.time() - start_time)
            except Exception as email_error:
                print(f"发送错误邮件失败: {str(email_error)}")
        raise
    finally:
        # 确保日志文件被正确关闭
        try:
            if logger is not None:
                print("\n正在关闭日志系统...")
                sys.stdout = sys.__stdout__  # 恢复标准输出
                logger.close()
                print("日志系统已关闭")
        except Exception as e:
            print(f"关闭日志系统时出错: {str(e)}")
            # 确保标准输出被恢复
            try:
                sys.stdout = sys.__stdout__
            except:
                pass

