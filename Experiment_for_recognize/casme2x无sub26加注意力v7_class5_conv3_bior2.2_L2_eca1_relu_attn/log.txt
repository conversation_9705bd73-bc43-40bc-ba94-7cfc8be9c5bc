日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m多头注意力融合[0m 🧠
  [96m- 建立跨模态注意力关系[0m 🔗
  [96m- 动态关注重要特征[0m 🎯
  [1;96m- 注意力头数: 8[0m 🎯
[1;92m🛑 启用早停机制:[0m
  [1;96m智能训练优化[0m ⚡
  [96m- 监控指标: accuracy[0m 📊
  [96m- 耐心值: 300 epochs[0m ⏳
  [96m- 改善阈值: 0.001[0m 📈
  [96m- 自动保存最佳模型[0m 💾
  [96m- 防止过拟合，提高训练效率[0m 🎯

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;92m🚀 GPU数据预加载[0m
[1;92m------------[0m
[1;96m📤 正在将所有数据预加载到GPU内存...[0m
[1;96m📊 训练数据大小: 0.27 GB[0m
[1;96m📊 测试数据大小: 0.06 GB[0m
[1;96m📊 总数据大小: 0.32 GB[0m
[1;96m⬆️ 正在上传训练数据到GPU...[0m
[1;96m⬆️ 正在上传测试数据到GPU...[0m
[1;92m✅ 数据预加载到GPU完成！[0m
[1;96m💾 GPU内存使用: GPU0: 0.3GB/79.3GB (0.4%)[0m
[1;96m⚡ GPU预加载模式：数据传输将极大加速！[0m

[1m[1;93m🛑 早停配置[0m
[1;93m--------[0m
[1;96m📊 监控指标: accuracy[0m
[1;96m⏳ 耐心值: 300 epochs[0m
[1;96m📈 最小改善阈值: 0.001[0m
[1;96m🎯 模式: 最大化[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 启用[0m
[1;96m📈 Epoch损失统计: 启用[0m
[1;96m📉 验证损失统计: 启用[0m
[1;96m🚨 过拟合监控: 启用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m✅ Epoch损失统计将显示: 是[0m
[1;96m✅ 验证损失统计将显示: 是[0m
[1;96m✅ 过拟合监控将显示: 是[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5147[0m (419/814)
   • 中间层1准确率: [96m0.4963[0m (404/814)
   • 中间层2准确率: [96m0.5172[0m (421/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.538623[0m
   • 中间损失1: [96m0.582702[0m
   • 中间损失2: [96m0.533434[0m
   • 蒸馏损失1 (KL): [95m0.220550[0m
   • 蒸馏损失2 (KL): [95m0.106017[0m
   • 特征损失1 (L2): [92m0.00367551[0m
   • 特征损失2 (L2): [92m0.00384606[0m
   • 总损失: [1;91m1.599141[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m27.08%[0m
   • 中间损失占比: [96m56.12%[0m
   • 蒸馏损失占比: [95m16.42%[0m
   • 特征损失占比: [92m0.38%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
📊 [1mUF1:[0m [1;92m0.3475[0m | [1mUAR:[0m [1;92m0.3619[0m
🥇 [1m最佳UF1:[0m [1;93m0.0000[0m | [1m最佳UAR:[0m [1;93m0.0000[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.814914[0m
   • 中间损失1: [96m1.275414[0m
   • 中间损失2: [96m0.817257[0m
   • 总验证损失: [1;91m2.907585[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.538623[0m
   • 验证损失: [94m0.814914[0m
   • 损失差值: [95m-0.276291[0m (训练-验证)
   • 损失比值: [96m0.6610[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.5882 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.3475 | UAR: 0.3619 | 准确率: 0.5882[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.5882 (最佳: 0.5882)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6929[0m (564/814)
   • 中间层1准确率: [96m0.6818[0m (555/814)
   • 中间层2准确率: [96m0.6990[0m (569/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.235988[0m
   • 中间损失1: [96m0.268129[0m
   • 中间损失2: [96m0.236930[0m
   • 蒸馏损失1 (KL): [95m0.247930[0m
   • 蒸馏损失2 (KL): [95m0.099642[0m
   • 特征损失1 (L2): [92m0.00500192[0m
   • 特征损失2 (L2): [92m0.00536142[0m
   • 总损失: [1;91m0.738831[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m21.47%[0m
   • 中间损失占比: [96m45.96%[0m
   • 蒸馏损失占比: [95m31.63%[0m
   • 特征损失占比: [92m0.94%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.5882[0m
📊 [1mUF1:[0m [1;92m0.5198[0m | [1mUAR:[0m [1;92m0.5778[0m
🥇 [1m最佳UF1:[0m [1;93m0.3475[0m | [1m最佳UAR:[0m [1;93m0.3619[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.464513[0m
   • 中间损失1: [96m1.139034[0m
   • 中间损失2: [96m0.900793[0m
   • 总验证损失: [1;91m2.504340[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.235988[0m
   • 验证损失: [94m0.464513[0m
   • 损失差值: [95m-0.228525[0m (训练-验证)
   • 损失比值: [96m0.5080[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.6765 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.5198 | UAR: 0.5778 | 准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.6765 (最佳: 0.6765)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7555[0m (615/814)
   • 中间层1准确率: [96m0.7432[0m (605/814)
   • 中间层2准确率: [96m0.7580[0m (617/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.146856[0m
   • 中间损失1: [96m0.166151[0m
   • 中间损失2: [96m0.147217[0m
   • 蒸馏损失1 (KL): [95m0.318475[0m
   • 蒸馏损失2 (KL): [95m0.096460[0m
   • 特征损失1 (L2): [92m0.00692588[0m
   • 特征损失2 (L2): [92m0.00690137[0m
   • 总损失: [1;91m0.482149[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m16.52%[0m
   • 中间损失占比: [96m35.25%[0m
   • 蒸馏损失占比: [95m46.68%[0m
   • 特征损失占比: [92m1.56%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4648[0m | [1mUAR:[0m [1;96m0.5000[0m
🥇 [1m最佳UF1:[0m [1;93m0.5198[0m | [1m最佳UAR:[0m [1;93m0.5778[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m1.017354[0m
   • 中间损失1: [96m2.727640[0m
   • 中间损失2: [96m0.907906[0m
   • 总验证损失: [1;91m4.652899[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.146856[0m
   • 验证损失: [94m1.017354[0m
   • 损失差值: [95m-0.870498[0m (训练-验证)
   • 损失比值: [96m0.1444[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (1/300) 剩余耐心: 299[0m

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8231[0m (670/814)
   • 中间层1准确率: [96m0.8120[0m (661/814)
   • 中间层2准确率: [96m0.8342[0m (679/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.086687[0m
   • 中间损失1: [96m0.092992[0m
   • 中间损失2: [96m0.079142[0m
   • 蒸馏损失1 (KL): [95m0.259257[0m
   • 蒸馏损失2 (KL): [95m0.089214[0m
   • 特征损失1 (L2): [92m0.00738283[0m
   • 特征损失2 (L2): [92m0.00745307[0m
   • 总损失: [1;91m0.287698[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m13.93%[0m
   • 中间损失占比: [96m27.67%[0m
   • 蒸馏损失占比: [95m56.01%[0m
   • 特征损失占比: [92m2.38%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;92m0.5750[0m | [1mUAR:[0m [1;92m0.5952[0m
🥇 [1m最佳UF1:[0m [1;93m0.5198[0m | [1m最佳UAR:[0m [1;93m0.5778[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.481424[0m
   • 中间损失1: [96m0.633137[0m
   • 中间损失2: [96m0.537237[0m
   • 总验证损失: [1;91m1.651798[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.086687[0m
   • 验证损失: [94m0.481424[0m
   • 损失差值: [95m-0.394737[0m (训练-验证)
   • 损失比值: [96m0.1801[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;92m💾 保存最佳模型，UF1和UAR都显著提升: UF1(0.5750↑) UAR(0.5952↑)[0m
[1;96m📊 UF1: 0.5750 | UAR: 0.5952 | 准确率: 0.6176[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth
[1;96m🔄 ⏳ 无改善 (2/300) 剩余耐心: 298[0m

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8280[0m (674/814)
   • 中间层1准确率: [96m0.8268[0m (673/814)
   • 中间层2准确率: [96m0.8366[0m (681/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.074091[0m
   • 中间损失1: [96m0.078752[0m
   • 中间损失2: [96m0.073006[0m
   • 蒸馏损失1 (KL): [95m0.251183[0m
   • 蒸馏损失2 (KL): [95m0.092061[0m
   • 特征损失1 (L2): [92m0.00794473[0m
   • 特征损失2 (L2): [92m0.00811780[0m
   • 总损失: [1;91m0.257142[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m12.66%[0m
   • 中间损失占比: [96m25.93%[0m
   • 蒸馏损失占比: [95m58.66%[0m
   • 特征损失占比: [92m2.74%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
📊 [1mUF1:[0m [1;92m0.6732[0m | [1mUAR:[0m [1;92m0.6952[0m
🥇 [1m最佳UF1:[0m [1;93m0.5750[0m | [1m最佳UAR:[0m [1;93m0.5952[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.749771[0m
   • 中间损失1: [96m2.652565[0m
   • 中间损失2: [96m0.638567[0m
   • 总验证损失: [1;91m4.040903[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.074091[0m
   • 验证损失: [94m0.749771[0m
   • 损失差值: [95m-0.675680[0m (训练-验证)
   • 损失比值: [96m0.0988[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.7647 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.6732 | UAR: 0.6952 | 准确率: 0.7647[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.7647 (最佳: 0.7647)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/casme2x无sub26加注意力v7_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8071[0m (657/814)
   • 中间层1准确率: [96m0.8108[0m (660/814)
   • 中间层2准确率: [96m0.8108[0m (660/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.131041[0m
   • 中间损失1: [96m0.146874[0m
   • 中间损失2: [96m0.137321[0m
   • 蒸馏损失1 (KL): [95m0.434171[0m
   • 蒸馏损失2 (KL): [95m0.104421[0m
   • 特征损失1 (L2): [92m0.00676967[0m
   • 特征损失2 (L2): [92m0.00700138[0m
   • 总损失: [1;91m0.449212[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m13.54%[0m
   • 中间损失占比: [96m29.37%[0m
   • 蒸馏损失占比: [95m55.66%[0m
   • 特征损失占比: [92m1.42%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6021[0m | [1mUAR:[0m [1;96m0.6206[0m
🥇 [1m最佳UF1:[0m [1;93m0.6732[0m | [1m最佳UAR:[0m [1;93m0.6952[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.414878[0m
   • 中间损失1: [96m0.901594[0m
   • 中间损失2: [96m0.387613[0m
   • 总验证损失: [1;91m1.704086[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.131041[0m
   • 验证损失: [94m0.414878[0m
   • 损失差值: [95m-0.283837[0m (训练-验证)
   • 损失比值: [96m0.3159[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (1/300) 剩余耐心: 299[0m

[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8575[0m (698/814)
   • 中间层1准确率: [96m0.8342[0m (679/814)
   • 中间层2准确率: [96m0.8624[0m (702/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.082830[0m
   • 中间损失1: [96m0.104964[0m
   • 中间损失2: [96m0.083542[0m
   • 蒸馏损失1 (KL): [95m0.361576[0m
   • 蒸馏损失2 (KL): [95m0.094082[0m
   • 特征损失1 (L2): [92m0.00554504[0m
   • 特征损失2 (L2): [92m0.00571716[0m
   • 总损失: [1;91m0.303791[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m11.22%[0m
   • 中间损失占比: [96m25.53%[0m
   • 蒸馏损失占比: [95m61.72%[0m
   • 特征损失占比: [92m1.53%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6109[0m | [1mUAR:[0m [1;96m0.6444[0m
🥇 [1m最佳UF1:[0m [1;93m0.6732[0m | [1m最佳UAR:[0m [1;93m0.6952[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.663510[0m
   • 中间损失1: [96m1.752162[0m
   • 中间损失2: [96m0.643807[0m
   • 总验证损失: [1;91m3.059479[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.082830[0m
   • 验证损失: [94m0.663510[0m
   • 损失差值: [95m-0.580680[0m (训练-验证)
   • 损失比值: [96m0.1248[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (2/300) 剩余耐心: 298[0m

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8882[0m (723/814)
   • 中间层1准确率: [96m0.8771[0m (714/814)
   • 中间层2准确率: [96m0.8784[0m (715/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.051821[0m
   • 中间损失1: [96m0.056060[0m
   • 中间损失2: [96m0.048963[0m
   • 蒸馏损失1 (KL): [95m0.269197[0m
   • 蒸馏损失2 (KL): [95m0.072904[0m
   • 特征损失1 (L2): [92m0.00688786[0m
   • 特征损失2 (L2): [92m0.00679857[0m
   • 总损失: [1;91m0.189313[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m10.11%[0m
   • 中间损失占比: [96m20.49%[0m
   • 蒸馏损失占比: [95m66.73%[0m
   • 特征损失占比: [92m2.67%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6625[0m | [1mUAR:[0m [1;96m0.6873[0m
🥇 [1m最佳UF1:[0m [1;93m0.6732[0m | [1m最佳UAR:[0m [1;93m0.6952[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.605005[0m
   • 中间损失1: [96m0.356670[0m
   • 中间损失2: [96m0.573996[0m
   • 总验证损失: [1;91m1.535672[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.051821[0m
   • 验证损失: [94m0.605005[0m
   • 损失差值: [95m-0.553184[0m (训练-验证)
   • 损失比值: [96m0.0857[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (3/300) 剩余耐心: 297[0m

[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9263[0m (754/814)
   • 中间层1准确率: [96m0.9177[0m (747/814)
   • 中间层2准确率: [96m0.9337[0m (760/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.034149[0m
   • 中间损失1: [96m0.038605[0m
   • 中间损失2: [96m0.033913[0m
   • 蒸馏损失1 (KL): [95m0.262073[0m
   • 蒸馏损失2 (KL): [95m0.075115[0m
   • 特征损失1 (L2): [92m0.00763403[0m
   • 特征损失2 (L2): [92m0.00815969[0m
   • 总损失: [1;91m0.143401[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m7.43%[0m
   • 中间损失占比: [96m15.78%[0m
   • 蒸馏损失占比: [95m73.36%[0m
   • 特征损失占比: [92m3.44%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5645[0m | [1mUAR:[0m [1;92m0.6968[0m
🥇 [1m最佳UF1:[0m [1;93m0.6732[0m | [1m最佳UAR:[0m [1;93m0.6952[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.787253[0m
   • 中间损失1: [96m0.588029[0m
   • 中间损失2: [96m0.683344[0m
   • 总验证损失: [1;91m2.058626[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.034149[0m
   • 验证损失: [94m0.787253[0m
   • 损失差值: [95m-0.753105[0m (训练-验证)
   • 损失比值: [96m0.0434[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (4/300) 剩余耐心: 296[0m

[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9054[0m (737/814)
   • 中间层1准确率: [96m0.8931[0m (727/814)
   • 中间层2准确率: [96m0.9103[0m (741/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.049920[0m
   • 中间损失1: [96m0.043073[0m
   • 中间损失2: [96m0.048821[0m
   • 蒸馏损失1 (KL): [95m0.245589[0m
   • 蒸馏损失2 (KL): [95m0.087441[0m
   • 特征损失1 (L2): [92m0.00805949[0m
   • 特征损失2 (L2): [92m0.00836571[0m
   • 总损失: [1;91m0.177293[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m10.16%[0m
   • 中间损失占比: [96m18.71%[0m
   • 蒸馏损失占比: [95m67.79%[0m
   • 特征损失占比: [92m3.34%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5633[0m | [1mUAR:[0m [1;96m0.5889[0m
🥇 [1m最佳UF1:[0m [1;93m0.6732[0m | [1m最佳UAR:[0m [1;93m0.6952[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m1.441068[0m
   • 中间损失1: [96m1.362019[0m
   • 中间损失2: [96m1.433809[0m
   • 总验证损失: [1;91m4.236897[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.049920[0m
   • 验证损失: [94m1.441068[0m
   • 损失差值: [95m-1.391149[0m (训练-验证)
   • 损失比值: [96m0.0346[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (5/300) 剩余耐心: 295[0m

[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9287[0m (756/814)
   • 中间层1准确率: [96m0.9005[0m (733/814)
   • 中间层2准确率: [96m0.9398[0m (765/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.039433[0m
   • 中间损失1: [96m0.051156[0m
   • 中间损失2: [96m0.036995[0m
   • 蒸馏损失1 (KL): [95m0.221476[0m
   • 蒸馏损失2 (KL): [95m0.069021[0m
   • 特征损失1 (L2): [92m0.00750807[0m
   • 特征损失2 (L2): [92m0.00810903[0m
   • 总损失: [1;91m0.159168[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m9.09%[0m
   • 中间损失占比: [96m20.33%[0m
   • 蒸馏损失占比: [95m66.98%[0m
   • 特征损失占比: [92m3.60%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6648[0m | [1mUAR:[0m [1;96m0.6937[0m
🥇 [1m最佳UF1:[0m [1;93m0.6732[0m | [1m最佳UAR:[0m [1;93m0.6952[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.491350[0m
   • 中间损失1: [96m0.415319[0m
   • 中间损失2: [96m0.643980[0m
   • 总验证损失: [1;91m1.550649[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.039433[0m
   • 验证损失: [94m0.491350[0m
   • 损失差值: [95m-0.451917[0m (训练-验证)
   • 损失比值: [96m0.0803[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (6/300) 剩余耐心: 294[0m

[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9275[0m (755/814)
   • 中间层1准确率: [96m0.8907[0m (725/814)
   • 中间层2准确率: [96m0.9398[0m (765/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.044863[0m
   • 中间损失1: [96m0.064327[0m
   • 中间损失2: [96m0.037911[0m
   • 蒸馏损失1 (KL): [95m0.348681[0m
   • 蒸馏损失2 (KL): [95m0.093335[0m
   • 特征损失1 (L2): [92m0.00739491[0m
   • 特征损失2 (L2): [92m0.00780830[0m
   • 总损失: [1;91m0.189078[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m7.42%[0m
   • 中间损失占比: [96m16.92%[0m
   • 蒸馏损失占比: [95m73.14%[0m
   • 特征损失占比: [92m2.52%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.4395[0m | [1mUAR:[0m [1;96m0.5508[0m
🥇 [1m最佳UF1:[0m [1;93m0.6732[0m | [1m最佳UAR:[0m [1;93m0.6952[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m2.812428[0m
   • 中间损失1: [96m1.534636[0m
   • 中间损失2: [96m2.242808[0m
   • 总验证损失: [1;91m6.589872[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.044863[0m
   • 验证损失: [94m2.812428[0m
   • 损失差值: [95m-2.767565[0m (训练-验证)
   • 损失比值: [96m0.0160[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (7/300) 剩余耐心: 293[0m

[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9238[0m (752/814)
   • 中间层1准确率: [96m0.8845[0m (720/814)
   • 中间层2准确率: [96m0.9115[0m (742/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.054572[0m
   • 中间损失1: [96m0.079082[0m
   • 中间损失2: [96m0.061437[0m
   • 蒸馏损失1 (KL): [95m0.501330[0m
   • 蒸馏损失2 (KL): [95m0.099866[0m
   • 特征损失1 (L2): [92m0.00631547[0m
   • 特征损失2 (L2): [92m0.00652066[0m
   • 总损失: [1;91m0.244185[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m6.74%[0m
   • 中间损失占比: [96m17.37%[0m
   • 蒸馏损失占比: [95m74.30%[0m
   • 特征损失占比: [92m1.59%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.4943[0m | [1mUAR:[0m [1;96m0.5270[0m
🥇 [1m最佳UF1:[0m [1;93m0.6732[0m | [1m最佳UAR:[0m [1;93m0.6952[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m1.519219[0m
   • 中间损失1: [96m2.173524[0m
   • 中间损失2: [96m1.825286[0m
   • 总验证损失: [1;91m5.518029[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.054572[0m
   • 验证损失: [94m1.519219[0m
   • 损失差值: [95m-1.464647[0m (训练-验证)
   • 损失比值: [96m0.0359[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (8/300) 剩余耐心: 292[0m

[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9595[0m (781/814)
   • 中间层1准确率: [96m0.9251[0m (753/814)
   • 中间层2准确率: [96m0.9447[0m (769/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.027734[0m
   • 中间损失1: [96m0.038947[0m
   • 中间损失2: [96m0.027520[0m
   • 蒸馏损失1 (KL): [95m0.332713[0m
   • 蒸馏损失2 (KL): [95m0.090157[0m
   • 特征损失1 (L2): [92m0.00709780[0m
   • 特征损失2 (L2): [92m0.00718390[0m
   • 总损失: [1;91m0.136496[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m5.22%[0m
   • 中间损失占比: [96m12.51%[0m
   • 蒸馏损失占比: [95m79.58%[0m
   • 特征损失占比: [92m2.69%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5200[0m | [1mUAR:[0m [1;96m0.6333[0m
🥇 [1m最佳UF1:[0m [1;93m0.6732[0m | [1m最佳UAR:[0m [1;93m0.6952[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m1.724248[0m
   • 中间损失1: [96m1.676819[0m
   • 中间损失2: [96m2.150380[0m
   • 总验证损失: [1;91m5.551446[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.027734[0m
   • 验证损失: [94m1.724248[0m
   • 损失差值: [95m-1.696514[0m (训练-验证)
   • 损失比值: [96m0.0161[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (9/300) 剩余耐心: 291[0m

[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9287[0m (756/814)
   • 中间层1准确率: [96m0.9263[0m (754/814)
   • 中间层2准确率: [96m0.9287[0m (756/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.033806[0m
   • 中间损失1: [96m0.033921[0m
   • 中间损失2: [96m0.032079[0m
   • 蒸馏损失1 (KL): [95m0.281448[0m
   • 蒸馏损失2 (KL): [95m0.081200[0m
   • 特征损失1 (L2): [92m0.00689136[0m
   • 特征损失2 (L2): [92m0.00693015[0m
   • 总损失: [1;91m0.136913[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m7.10%[0m
   • 中间损失占比: [96m13.86%[0m
   • 蒸馏损失占比: [95m76.14%[0m
   • 特征损失占比: [92m2.90%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5611[0m | [1mUAR:[0m [1;96m0.6381[0m
🥇 [1m最佳UF1:[0m [1;93m0.6732[0m | [1m最佳UAR:[0m [1;93m0.6952[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m2.043654[0m
   • 中间损失1: [96m3.719791[0m
   • 中间损失2: [96m2.100652[0m
   • 总验证损失: [1;91m7.864096[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.033806[0m
   • 验证损失: [94m2.043654[0m
   • 损失差值: [95m-2.009848[0m (训练-验证)
   • 损失比值: [96m0.0165[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (10/300) 剩余耐心: 290[0m

[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m
