日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用

[96m──────────────────────────────────────────────────[0m
[1m[96m🚀 GPU数据预加载配置[0m
[96m──────────────────────────────────────────────────[0m
[92m✅ GPU数据预加载功能已启用[0m
[36mℹ️ 将在训练开始前预加载所有数据到GPU显存[0m
[36mℹ️ 这将显著提升训练速度，但需要足够的GPU显存[0m
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[95m──────────────────────────────────────────────────[0m
[1m[95m📊 损失函数配置[0m
[95m──────────────────────────────────────────────────[0m
[92m⚖️ 使用加权Focal Loss (3分类)[0m
[36m🎯 类别权重: ['0.3309', '0.0956', '0.5735'][0m
开始训练...

[96m──────────────────────────────────────────────────[0m
[1m[96m🚀 GPU数据预加载器初始化[0m
[96m──────────────────────────────────────────────────[0m
[36mℹ️ 数据集路径: ./SAMM_LOSO_full[0m
[36mℹ️ 分类数量: 3[0m
[36mℹ️ 目标设备: cuda[0m

[92m======================[0m
[1m[92m🚀 开始预加载SAMM数据集到GPU显存 🚀[0m
[92m======================[0m

[36m📊 正在估算数据集显存需求...[0m
[91m❌ 数据预加载器初始化失败: [Errno 2] No such file or directory: './SAMM_LOSO_full'[0m
[93m⚠️ 将使用传统训练模式[0m
根据配置,不使用Visdom可视化
警告: 找不到主体 011 的数据目录,跳过
警告: 找不到主体 006 的数据目录,跳过
警告: 找不到主体 014 的数据目录,跳过
警告: 找不到主体 026 的数据目录,跳过
警告: 找不到主体 007 的数据目录,跳过
警告: 找不到主体 035 的数据目录,跳过
警告: 找不到主体 013 的数据目录,跳过
警告: 找不到主体 016 的数据目录,跳过
警告: 找不到主体 022 的数据目录,跳过
警告: 找不到主体 033 的数据目录,跳过
警告: 找不到主体 009 的数据目录,跳过
警告: 找不到主体 010 的数据目录,跳过
警告: 找不到主体 017 的数据目录,跳过
警告: 找不到主体 020 的数据目录,跳过
警告: 找不到主体 012 的数据目录,跳过
警告: 找不到主体 015 的数据目录,跳过
警告: 找不到主体 018 的数据目录,跳过
警告: 找不到主体 030 的数据目录,跳过
警告: 找不到主体 032 的数据目录,跳过
警告: 找不到主体 034 的数据目录,跳过
警告: 找不到主体 021 的数据目录,跳过
警告: 找不到主体 028 的数据目录,跳过
警告: 找不到主体 019 的数据目录,跳过
警告: 找不到主体 023 的数据目录,跳过
警告: 找不到主体 024 的数据目录,跳过
警告: 找不到主体 031 的数据目录,跳过
警告: 找不到主体 036 的数据目录,跳过
警告: 找不到主体 037 的数据目录,跳过

================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8
时间: 2025-08-03 12:26:48
总训练时间: 00:00:00
数据集: SAMM_LOSO_full

【系统环境】
操作系统: 💻 Linux 6.8.0-1031-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 62.7GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------

【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
   算术平均       nan        nan        nan    

    注意     算术平均值仅供参考，总体性能以上方报告的UF1/UAR为准


【各受试者详细预测结果】
==================================================

================================================================================

【实验结果报告】
================================================================================

================================================================================
【实验结果报告 - 3分类 (正性、负性、惊讶)】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8
分类方案: 3分类 (正性、负性、惊讶)
时间: 2025-08-03 12:26:48
总训练时间: 00:00:00
数据集: SAMM_LOSO_full


【系统环境】
操作系统: 💻 Linux 6.8.0-1031-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 62.7GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU数量: 1
训练模式: 单设备

【模型配置】
模型架构: SKD_TSTSAN
分类数量: 3
是否使用预训练: 是
预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【数据增强配置】
使用训练数据增强: 是
使用测试数据增强: 是
旋转角度范围: 3,8
训练增强倍数: 7,2,10
测试增强倍数: 7,2,10
测试数据镜像训练: 启用
镜像训练受试者: 017,014,007,020,006

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
   算术平均       nan        nan        nan    

    注意     算术平均值仅供参考，总体性能以上方报告的UF1/UAR为准

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [3分类 (正性、负性、惊讶)实验总结] SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8 - UF1=N/A, UAR=N/A

训练完成!

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用

[96m──────────────────────────────────────────────────[0m
[1m[96m🚀 GPU数据预加载配置[0m
[96m──────────────────────────────────────────────────[0m
[92m✅ GPU数据预加载功能已启用[0m
[36mℹ️ 将在训练开始前预加载所有数据到GPU显存[0m
[36mℹ️ 这将显著提升训练速度，但需要足够的GPU显存[0m
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[95m──────────────────────────────────────────────────[0m
[1m[95m📊 损失函数配置[0m
[95m──────────────────────────────────────────────────[0m
[92m⚖️ 使用加权Focal Loss (3分类)[0m
[36m🎯 类别权重: ['0.3309', '0.0956', '0.5735'][0m
开始训练...

[96m──────────────────────────────────────────────────[0m
[1m[96m🚀 GPU数据预加载器初始化[0m
[96m──────────────────────────────────────────────────[0m
[36mℹ️ 数据集路径: /home/<USER>/data/ajq/SKD-TSTSAN-data/SAMM_LOSO_full[0m
[36mℹ️ 分类数量: 3[0m
[36mℹ️ 目标设备: cuda[0m

[92m======================[0m
[1m[92m🚀 开始预加载SAMM数据集到GPU显存 🚀[0m
[92m======================[0m

[36m📊 正在估算数据集显存需求...[0m
[92m📈 估算结果: 3668 个样本[0m
[92m💾 预计显存需求: 1.20 GB[0m
[36mℹ️ GPU总显存: 79.25 GB[0m
[36mℹ️ 可用显存: 63.40 GB[0m
[36mℹ️ 发现 29 个受试者: ['006', '007', '009', '010', '011', '012', '013', '014', '015', '016', '017', '018', '019', '020', '021', '022', '023', '024', '026', '028', '030', '031', '032', '033', '034', '035', '036', '037', 'Experiment_for_recognize'][0m
[36m📊 预加载进度: 1/29 - 006[0m
[36m📥 正在预加载受试者 006 的数据...[0m
[92m✅ 受试者 006 数据预加载完成[0m
[36mℹ️ 训练样本: 120, 测试样本: 11[0m
[36m💾 当前显存使用: 0.04 GB[0m
[36m📊 预加载进度: 2/29 - 007[0m
[36m📥 正在预加载受试者 007 的数据...[0m
[92m✅ 受试者 007 数据预加载完成[0m
[36mℹ️ 训练样本: 123, 测试样本: 8[0m
[36m💾 当前显存使用: 0.09 GB[0m
[36m📊 预加载进度: 3/29 - 009[0m
[36m📥 正在预加载受试者 009 的数据...[0m
[92m✅ 受试者 009 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.13 GB[0m
[36m📊 预加载进度: 4/29 - 010[0m
[36m📥 正在预加载受试者 010 的数据...[0m
[92m✅ 受试者 010 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.17 GB[0m
[36m📊 预加载进度: 5/29 - 011[0m
[36m📥 正在预加载受试者 011 的数据...[0m
[92m✅ 受试者 011 数据预加载完成[0m
[36mℹ️ 训练样本: 111, 测试样本: 20[0m
[36m💾 当前显存使用: 0.22 GB[0m
[36m📊 预加载进度: 6/29 - 012[0m
[36m📥 正在预加载受试者 012 的数据...[0m
[92m✅ 受试者 012 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.26 GB[0m
[36m📊 预加载进度: 7/29 - 013[0m
[36m📥 正在预加载受试者 013 的数据...[0m
[92m✅ 受试者 013 数据预加载完成[0m
[36mℹ️ 训练样本: 125, 测试样本: 6[0m
[36m💾 当前显存使用: 0.30 GB[0m
[36m📊 预加载进度: 8/29 - 014[0m
[36m📥 正在预加载受试者 014 的数据...[0m
[92m✅ 受试者 014 数据预加载完成[0m
[36mℹ️ 训练样本: 121, 测试样本: 10[0m
[36m💾 当前显存使用: 0.34 GB[0m
[36m📊 预加载进度: 9/29 - 015[0m
[36m📥 正在预加载受试者 015 的数据...[0m
[92m✅ 受试者 015 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.39 GB[0m
[36m📊 预加载进度: 10/29 - 016[0m
[36m📥 正在预加载受试者 016 的数据...[0m
[92m✅ 受试者 016 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 0.43 GB[0m
[36m📊 预加载进度: 11/29 - 017[0m
[36m📥 正在预加载受试者 017 的数据...[0m
[92m✅ 受试者 017 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.47 GB[0m
[36m📊 预加载进度: 12/29 - 018[0m
[36m📥 正在预加载受试者 018 的数据...[0m
[92m✅ 受试者 018 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.52 GB[0m
[36m📊 预加载进度: 13/29 - 019[0m
[36m📥 正在预加载受试者 019 的数据...[0m
[92m✅ 受试者 019 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.56 GB[0m
[36m📊 预加载进度: 14/29 - 020[0m
[36m📥 正在预加载受试者 020 的数据...[0m
[92m✅ 受试者 020 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.60 GB[0m
[36m📊 预加载进度: 15/29 - 021[0m
[36m📥 正在预加载受试者 021 的数据...[0m
[92m✅ 受试者 021 数据预加载完成[0m
[36mℹ️ 训练样本: 129, 测试样本: 2[0m
[36m💾 当前显存使用: 0.65 GB[0m
[36m📊 预加载进度: 16/29 - 022[0m
[36m📥 正在预加载受试者 022 的数据...[0m
[92m✅ 受试者 022 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 0.69 GB[0m
[36m📊 预加载进度: 17/29 - 023[0m
[36m📥 正在预加载受试者 023 的数据...[0m
[92m✅ 受试者 023 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.73 GB[0m
[36m📊 预加载进度: 18/29 - 024[0m
[36m📥 正在预加载受试者 024 的数据...[0m
[92m✅ 受试者 024 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.78 GB[0m
[36m📊 预加载进度: 19/29 - 026[0m
[36m📥 正在预加载受试者 026 的数据...[0m
[92m✅ 受试者 026 数据预加载完成[0m
[36mℹ️ 训练样本: 122, 测试样本: 9[0m
[36m💾 当前显存使用: 0.82 GB[0m
[36m📊 预加载进度: 20/29 - 028[0m
[36m📥 正在预加载受试者 028 的数据...[0m
[92m✅ 受试者 028 数据预加载完成[0m
[36mℹ️ 训练样本: 129, 测试样本: 2[0m
[36m💾 当前显存使用: 0.86 GB[0m
[36m📊 预加载进度: 21/29 - 030[0m
[36m📥 正在预加载受试者 030 的数据...[0m
[92m✅ 受试者 030 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.91 GB[0m
[36m📊 预加载进度: 22/29 - 031[0m
[36m📥 正在预加载受试者 031 的数据...[0m
[92m✅ 受试者 031 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.95 GB[0m
[36m📊 预加载进度: 23/29 - 032[0m
[36m📥 正在预加载受试者 032 的数据...[0m
[92m✅ 受试者 032 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.99 GB[0m
[36m📊 预加载进度: 24/29 - 033[0m
[36m📥 正在预加载受试者 033 的数据...[0m
[92m✅ 受试者 033 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 1.03 GB[0m
[36m📊 预加载进度: 25/29 - 034[0m
[36m📥 正在预加载受试者 034 的数据...[0m
[92m✅ 受试者 034 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 1.08 GB[0m
[36m📊 预加载进度: 26/29 - 035[0m
[36m📥 正在预加载受试者 035 的数据...[0m
[92m✅ 受试者 035 数据预加载完成[0m
[36mℹ️ 训练样本: 123, 测试样本: 8[0m
[36m💾 当前显存使用: 1.12 GB[0m
[36m📊 预加载进度: 27/29 - 036[0m
[36m📥 正在预加载受试者 036 的数据...[0m
[92m✅ 受试者 036 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 1.16 GB[0m
[36m📊 预加载进度: 28/29 - 037[0m
[36m📥 正在预加载受试者 037 的数据...[0m
[92m✅ 受试者 037 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 1.21 GB[0m
[36m📊 预加载进度: 29/29 - Experiment_for_recognize[0m
[36m📥 正在预加载受试者 Experiment_for_recognize 的数据...[0m
[92m✅ 受试者 Experiment_for_recognize 数据预加载完成[0m
[36mℹ️ 训练样本: 0, 测试样本: 0[0m
[36m💾 当前显存使用: 1.21 GB[0m

[92m===========[0m
[1m[92m✅ 数据预加载完成 ✅[0m
[92m===========[0m

[92m✅ 成功预加载: 29 个受试者[0m
[1m[95m💾 最终显存使用: 1.21 GB[0m
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: 011
========================================
组别: 大样本组(>8个测试样本)
[92m🚀 使用GPU预加载数据[0m
[36mℹ️ 预加载数据 - 训练样本: 111, 测试样本: 20[0m

训练过程出错: local variable 'original_test_labels' referenced before assignment
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANSAMM.py", line 1622, in <module>
    results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions_SAMM.py", line 1636, in main_SKD_TSTSAN_with_Aug_with_SKD
    original_test_labels.append(label)  # 记录原始标签
UnboundLocalError: local variable 'original_test_labels' referenced before assignment


【实验结果报告】
================================================================================

================================================================================
【实验结果报告 - 3分类 (正性、负性、惊讶)】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8
分类方案: 3分类 (正性、负性、惊讶)
时间: 2025-08-03 12:35:57
总训练时间: 00:01:12
数据集: SAMM_LOSO_full


【系统环境】
操作系统: 💻 Linux 6.8.0-1031-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 62.7GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU数量: 1
训练模式: 单设备

【模型配置】
模型架构: SKD_TSTSAN
分类数量: 3
是否使用预训练: 是
预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【数据增强配置】
使用训练数据增强: 是
使用测试数据增强: 是
旋转角度范围: 3,8
训练增强倍数: 7,2,10
测试增强倍数: 7,2,10
测试数据镜像训练: 启用
镜像训练受试者: 017,014,007,020,006

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 0.0000
- UAR分数: 0.0000

【各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
   算术平均       nan        nan        nan    

    注意     算术平均值仅供参考，总体性能以上方报告的UF1/UAR为准

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [3分类 (正性、负性、惊讶)实验总结] SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8 - UF1=0.0000, UAR=0.0000

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用

[96m──────────────────────────────────────────────────[0m
[1m[96m🚀 GPU数据预加载配置[0m
[96m──────────────────────────────────────────────────[0m
[92m✅ GPU数据预加载功能已启用[0m
[36mℹ️ 将在训练开始前预加载所有数据到GPU显存[0m
[36mℹ️ 这将显著提升训练速度，但需要足够的GPU显存[0m
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[95m──────────────────────────────────────────────────[0m
[1m[95m📊 损失函数配置[0m
[95m──────────────────────────────────────────────────[0m
[92m⚖️ 使用加权Focal Loss (3分类)[0m
[36m🎯 类别权重: ['0.3309', '0.0956', '0.5735'][0m
开始训练...

[96m──────────────────────────────────────────────────[0m
[1m[96m🚀 GPU数据预加载器初始化[0m
[96m──────────────────────────────────────────────────[0m
[36mℹ️ 数据集路径: /home/<USER>/data/ajq/SKD-TSTSAN-data/SAMM_LOSO_full[0m
[36mℹ️ 分类数量: 3[0m
[36mℹ️ 目标设备: cuda[0m

[92m======================[0m
[1m[92m🚀 开始预加载SAMM数据集到GPU显存 🚀[0m
[92m======================[0m

[36m📊 正在估算数据集显存需求...[0m
[92m📈 估算结果: 3668 个样本[0m
[92m💾 预计显存需求: 1.20 GB[0m
[36mℹ️ GPU总显存: 79.25 GB[0m
[36mℹ️ 可用显存: 63.40 GB[0m
[36mℹ️ 发现 29 个受试者: ['006', '007', '009', '010', '011', '012', '013', '014', '015', '016', '017', '018', '019', '020', '021', '022', '023', '024', '026', '028', '030', '031', '032', '033', '034', '035', '036', '037', 'Experiment_for_recognize'][0m
[36m📊 预加载进度: 1/29 - 006[0m
[36m📥 正在预加载受试者 006 的数据...[0m
[92m✅ 受试者 006 数据预加载完成[0m
[36mℹ️ 训练样本: 120, 测试样本: 11[0m
[36m💾 当前显存使用: 0.04 GB[0m
[36m📊 预加载进度: 2/29 - 007[0m
[36m📥 正在预加载受试者 007 的数据...[0m
[92m✅ 受试者 007 数据预加载完成[0m
[36mℹ️ 训练样本: 123, 测试样本: 8[0m
[36m💾 当前显存使用: 0.09 GB[0m
[36m📊 预加载进度: 3/29 - 009[0m
[36m📥 正在预加载受试者 009 的数据...[0m
[92m✅ 受试者 009 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.13 GB[0m
[36m📊 预加载进度: 4/29 - 010[0m
[36m📥 正在预加载受试者 010 的数据...[0m
[92m✅ 受试者 010 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.17 GB[0m
[36m📊 预加载进度: 5/29 - 011[0m
[36m📥 正在预加载受试者 011 的数据...[0m
[92m✅ 受试者 011 数据预加载完成[0m
[36mℹ️ 训练样本: 111, 测试样本: 20[0m
[36m💾 当前显存使用: 0.22 GB[0m
[36m📊 预加载进度: 6/29 - 012[0m
[36m📥 正在预加载受试者 012 的数据...[0m
[92m✅ 受试者 012 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.26 GB[0m
[36m📊 预加载进度: 7/29 - 013[0m
[36m📥 正在预加载受试者 013 的数据...[0m
[92m✅ 受试者 013 数据预加载完成[0m
[36mℹ️ 训练样本: 125, 测试样本: 6[0m
[36m💾 当前显存使用: 0.30 GB[0m
[36m📊 预加载进度: 8/29 - 014[0m
[36m📥 正在预加载受试者 014 的数据...[0m
[92m✅ 受试者 014 数据预加载完成[0m
[36mℹ️ 训练样本: 121, 测试样本: 10[0m
[36m💾 当前显存使用: 0.34 GB[0m
[36m📊 预加载进度: 9/29 - 015[0m
[36m📥 正在预加载受试者 015 的数据...[0m
[92m✅ 受试者 015 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.39 GB[0m
[36m📊 预加载进度: 10/29 - 016[0m
[36m📥 正在预加载受试者 016 的数据...[0m
[92m✅ 受试者 016 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 0.43 GB[0m
[36m📊 预加载进度: 11/29 - 017[0m
[36m📥 正在预加载受试者 017 的数据...[0m
[92m✅ 受试者 017 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.47 GB[0m
[36m📊 预加载进度: 12/29 - 018[0m
[36m📥 正在预加载受试者 018 的数据...[0m
[92m✅ 受试者 018 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.52 GB[0m
[36m📊 预加载进度: 13/29 - 019[0m
[36m📥 正在预加载受试者 019 的数据...[0m
[92m✅ 受试者 019 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.56 GB[0m
[36m📊 预加载进度: 14/29 - 020[0m
[36m📥 正在预加载受试者 020 的数据...[0m
[92m✅ 受试者 020 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.60 GB[0m
[36m📊 预加载进度: 15/29 - 021[0m
[36m📥 正在预加载受试者 021 的数据...[0m
[92m✅ 受试者 021 数据预加载完成[0m
[36mℹ️ 训练样本: 129, 测试样本: 2[0m
[36m💾 当前显存使用: 0.65 GB[0m
[36m📊 预加载进度: 16/29 - 022[0m
[36m📥 正在预加载受试者 022 的数据...[0m
[92m✅ 受试者 022 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 0.69 GB[0m
[36m📊 预加载进度: 17/29 - 023[0m
[36m📥 正在预加载受试者 023 的数据...[0m
[92m✅ 受试者 023 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.73 GB[0m
[36m📊 预加载进度: 18/29 - 024[0m
[36m📥 正在预加载受试者 024 的数据...[0m
[92m✅ 受试者 024 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.78 GB[0m
[36m📊 预加载进度: 19/29 - 026[0m
[36m📥 正在预加载受试者 026 的数据...[0m
[92m✅ 受试者 026 数据预加载完成[0m
[36mℹ️ 训练样本: 122, 测试样本: 9[0m
[36m💾 当前显存使用: 0.82 GB[0m
[36m📊 预加载进度: 20/29 - 028[0m
[36m📥 正在预加载受试者 028 的数据...[0m
[92m✅ 受试者 028 数据预加载完成[0m
[36mℹ️ 训练样本: 129, 测试样本: 2[0m
[36m💾 当前显存使用: 0.86 GB[0m
[36m📊 预加载进度: 21/29 - 030[0m
[36m📥 正在预加载受试者 030 的数据...[0m
[92m✅ 受试者 030 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.91 GB[0m
[36m📊 预加载进度: 22/29 - 031[0m
[36m📥 正在预加载受试者 031 的数据...[0m
[92m✅ 受试者 031 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.95 GB[0m
[36m📊 预加载进度: 23/29 - 032[0m
[36m📥 正在预加载受试者 032 的数据...[0m
[92m✅ 受试者 032 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.99 GB[0m
[36m📊 预加载进度: 24/29 - 033[0m
[36m📥 正在预加载受试者 033 的数据...[0m
[92m✅ 受试者 033 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 1.03 GB[0m
[36m📊 预加载进度: 25/29 - 034[0m
[36m📥 正在预加载受试者 034 的数据...[0m
[92m✅ 受试者 034 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 1.08 GB[0m
[36m📊 预加载进度: 26/29 - 035[0m
[36m📥 正在预加载受试者 035 的数据...[0m
[92m✅ 受试者 035 数据预加载完成[0m
[36mℹ️ 训练样本: 123, 测试样本: 8[0m
[36m💾 当前显存使用: 1.12 GB[0m
[36m📊 预加载进度: 27/29 - 036[0m
[36m📥 正在预加载受试者 036 的数据...[0m
[92m✅ 受试者 036 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 1.16 GB[0m
[36m📊 预加载进度: 28/29 - 037[0m
[36m📥 正在预加载受试者 037 的数据...[0m
[92m✅ 受试者 037 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 1.21 GB[0m
[36m📊 预加载进度: 29/29 - Experiment_for_recognize[0m
[36m📥 正在预加载受试者 Experiment_for_recognize 的数据...[0m
[92m✅ 受试者 Experiment_for_recognize 数据预加载完成[0m
[36mℹ️ 训练样本: 0, 测试样本: 0[0m
[36m💾 当前显存使用: 1.21 GB[0m

[92m===========[0m
[1m[92m✅ 数据预加载完成 ✅[0m
[92m===========[0m

[92m✅ 成功预加载: 29 个受试者[0m
[1m[95m💾 最终显存使用: 1.21 GB[0m
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: 011
========================================
组别: 大样本组(>8个测试样本)
[92m🚀 使用GPU预加载数据[0m
[36mℹ️ 预加载数据 - 训练样本: 111, 测试样本: 20[0m

011测试标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0]
创建受试者目录: 011
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8/011/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8/011/logs

[94m──────────────────────────────────────────────────[0m
[1m[94m🤖 模型初始化[0m
[94m──────────────────────────────────────────────────[0m
[36m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [36m- 通过小波变换实现多频响应特征提取[0m 🌊
  [36m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [36m- 实现非常大的感受野而不会过度参数化[0m 📡
  [36m- 特别适合微表情的频域特征分析[0m 📊
[33m🚫 未启用跨分支交互[0m
  [36m- 保持原始三分支独立处理[0m 📦
  [36m- 最快推理速度，最低内存占用[0m ⚡
  [36m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

训练过程出错: only one element tensors can be converted to Python scalars
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANSAMM.py", line 1622, in <module>
    results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions_SAMM.py", line 1988, in main_SKD_TSTSAN_with_Aug_with_SKD
    X_train = torch.Tensor(X_train).permute(0, 3, 1, 2)
ValueError: only one element tensors can be converted to Python scalars


【实验结果报告】
================================================================================

================================================================================
【实验结果报告 - 3分类 (正性、负性、惊讶)】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8
分类方案: 3分类 (正性、负性、惊讶)
时间: 2025-08-03 12:39:28
总训练时间: 00:01:09
数据集: SAMM_LOSO_full


【系统环境】
操作系统: 💻 Linux 6.8.0-1031-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 62.7GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU数量: 1
训练模式: 单设备

【模型配置】
模型架构: SKD_TSTSAN
分类数量: 3
是否使用预训练: 是
预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【数据增强配置】
使用训练数据增强: 是
使用测试数据增强: 是
旋转角度范围: 3,8
训练增强倍数: 7,2,10
测试增强倍数: 7,2,10
测试数据镜像训练: 启用
镜像训练受试者: 017,014,007,020,006

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 0.0000
- UAR分数: 0.0000

【各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
   算术平均       nan        nan        nan    

    注意     算术平均值仅供参考，总体性能以上方报告的UF1/UAR为准

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [3分类 (正性、负性、惊讶)实验总结] SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8 - UF1=0.0000, UAR=0.0000

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用

[96m──────────────────────────────────────────────────[0m
[1m[96m🚀 GPU数据预加载配置[0m
[96m──────────────────────────────────────────────────[0m
[92m✅ GPU数据预加载功能已启用[0m
[36mℹ️ 将在训练开始前预加载所有数据到GPU显存[0m
[36mℹ️ 这将显著提升训练速度，但需要足够的GPU显存[0m
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[95m──────────────────────────────────────────────────[0m
[1m[95m📊 损失函数配置[0m
[95m──────────────────────────────────────────────────[0m
[92m⚖️ 使用加权Focal Loss (3分类)[0m
[36m🎯 类别权重: ['0.3309', '0.0956', '0.5735'][0m
开始训练...

[96m──────────────────────────────────────────────────[0m
[1m[96m🚀 GPU数据预加载器初始化[0m
[96m──────────────────────────────────────────────────[0m
[36mℹ️ 数据集路径: /home/<USER>/data/ajq/SKD-TSTSAN-data/SAMM_LOSO_full[0m
[36mℹ️ 分类数量: 3[0m
[36mℹ️ 目标设备: cuda[0m

[92m======================[0m
[1m[92m🚀 开始预加载SAMM数据集到GPU显存 🚀[0m
[92m======================[0m

[36m📊 正在估算数据集显存需求...[0m
[92m📈 估算结果: 3668 个样本[0m
[92m💾 预计显存需求: 1.20 GB[0m
[36mℹ️ GPU总显存: 79.25 GB[0m
[36mℹ️ 可用显存: 63.40 GB[0m
[36mℹ️ 发现 29 个受试者: ['006', '007', '009', '010', '011', '012', '013', '014', '015', '016', '017', '018', '019', '020', '021', '022', '023', '024', '026', '028', '030', '031', '032', '033', '034', '035', '036', '037', 'Experiment_for_recognize'][0m
[36m📊 预加载进度: 1/29 - 006[0m
[36m📥 正在预加载受试者 006 的数据...[0m
[92m✅ 受试者 006 数据预加载完成[0m
[36mℹ️ 训练样本: 120, 测试样本: 11[0m
[36m💾 当前显存使用: 0.04 GB[0m
[36m📊 预加载进度: 2/29 - 007[0m
[36m📥 正在预加载受试者 007 的数据...[0m
[92m✅ 受试者 007 数据预加载完成[0m
[36mℹ️ 训练样本: 123, 测试样本: 8[0m
[36m💾 当前显存使用: 0.09 GB[0m
[36m📊 预加载进度: 3/29 - 009[0m
[36m📥 正在预加载受试者 009 的数据...[0m
[92m✅ 受试者 009 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.13 GB[0m
[36m📊 预加载进度: 4/29 - 010[0m
[36m📥 正在预加载受试者 010 的数据...[0m
[92m✅ 受试者 010 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.17 GB[0m
[36m📊 预加载进度: 5/29 - 011[0m
[36m📥 正在预加载受试者 011 的数据...[0m
[92m✅ 受试者 011 数据预加载完成[0m
[36mℹ️ 训练样本: 111, 测试样本: 20[0m
[36m💾 当前显存使用: 0.22 GB[0m
[36m📊 预加载进度: 6/29 - 012[0m
[36m📥 正在预加载受试者 012 的数据...[0m
[92m✅ 受试者 012 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.26 GB[0m
[36m📊 预加载进度: 7/29 - 013[0m
[36m📥 正在预加载受试者 013 的数据...[0m
[92m✅ 受试者 013 数据预加载完成[0m
[36mℹ️ 训练样本: 125, 测试样本: 6[0m
[36m💾 当前显存使用: 0.30 GB[0m
[36m📊 预加载进度: 8/29 - 014[0m
[36m📥 正在预加载受试者 014 的数据...[0m
[92m✅ 受试者 014 数据预加载完成[0m
[36mℹ️ 训练样本: 121, 测试样本: 10[0m
[36m💾 当前显存使用: 0.34 GB[0m
[36m📊 预加载进度: 9/29 - 015[0m
[36m📥 正在预加载受试者 015 的数据...[0m
[92m✅ 受试者 015 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.39 GB[0m
[36m📊 预加载进度: 10/29 - 016[0m
[36m📥 正在预加载受试者 016 的数据...[0m
[92m✅ 受试者 016 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 0.43 GB[0m
[36m📊 预加载进度: 11/29 - 017[0m
[36m📥 正在预加载受试者 017 的数据...[0m
[92m✅ 受试者 017 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.47 GB[0m
[36m📊 预加载进度: 12/29 - 018[0m
[36m📥 正在预加载受试者 018 的数据...[0m
[92m✅ 受试者 018 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.52 GB[0m
[36m📊 预加载进度: 13/29 - 019[0m
[36m📥 正在预加载受试者 019 的数据...[0m
[92m✅ 受试者 019 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.56 GB[0m
[36m📊 预加载进度: 14/29 - 020[0m
[36m📥 正在预加载受试者 020 的数据...[0m
[92m✅ 受试者 020 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.60 GB[0m
[36m📊 预加载进度: 15/29 - 021[0m
[36m📥 正在预加载受试者 021 的数据...[0m
[92m✅ 受试者 021 数据预加载完成[0m
[36mℹ️ 训练样本: 129, 测试样本: 2[0m
[36m💾 当前显存使用: 0.65 GB[0m
[36m📊 预加载进度: 16/29 - 022[0m
[36m📥 正在预加载受试者 022 的数据...[0m
[92m✅ 受试者 022 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 0.69 GB[0m
[36m📊 预加载进度: 17/29 - 023[0m
[36m📥 正在预加载受试者 023 的数据...[0m
[92m✅ 受试者 023 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.73 GB[0m
[36m📊 预加载进度: 18/29 - 024[0m
[36m📥 正在预加载受试者 024 的数据...[0m
[92m✅ 受试者 024 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.78 GB[0m
[36m📊 预加载进度: 19/29 - 026[0m
[36m📥 正在预加载受试者 026 的数据...[0m
[92m✅ 受试者 026 数据预加载完成[0m
[36mℹ️ 训练样本: 122, 测试样本: 9[0m
[36m💾 当前显存使用: 0.82 GB[0m
[36m📊 预加载进度: 20/29 - 028[0m
[36m📥 正在预加载受试者 028 的数据...[0m
[92m✅ 受试者 028 数据预加载完成[0m
[36mℹ️ 训练样本: 129, 测试样本: 2[0m
[36m💾 当前显存使用: 0.86 GB[0m
[36m📊 预加载进度: 21/29 - 030[0m
[36m📥 正在预加载受试者 030 的数据...[0m
[92m✅ 受试者 030 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.91 GB[0m
[36m📊 预加载进度: 22/29 - 031[0m
[36m📥 正在预加载受试者 031 的数据...[0m
[92m✅ 受试者 031 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.95 GB[0m
[36m📊 预加载进度: 23/29 - 032[0m
[36m📥 正在预加载受试者 032 的数据...[0m
[92m✅ 受试者 032 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.99 GB[0m
[36m📊 预加载进度: 24/29 - 033[0m
[36m📥 正在预加载受试者 033 的数据...[0m
[92m✅ 受试者 033 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 1.03 GB[0m
[36m📊 预加载进度: 25/29 - 034[0m
[36m📥 正在预加载受试者 034 的数据...[0m
[92m✅ 受试者 034 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 1.08 GB[0m
[36m📊 预加载进度: 26/29 - 035[0m
[36m📥 正在预加载受试者 035 的数据...[0m
[92m✅ 受试者 035 数据预加载完成[0m
[36mℹ️ 训练样本: 123, 测试样本: 8[0m
[36m💾 当前显存使用: 1.12 GB[0m
[36m📊 预加载进度: 27/29 - 036[0m
[36m📥 正在预加载受试者 036 的数据...[0m
[92m✅ 受试者 036 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 1.16 GB[0m
[36m📊 预加载进度: 28/29 - 037[0m
[36m📥 正在预加载受试者 037 的数据...[0m
[92m✅ 受试者 037 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 1.21 GB[0m
[36m📊 预加载进度: 29/29 - Experiment_for_recognize[0m
[36m📥 正在预加载受试者 Experiment_for_recognize 的数据...[0m
[92m✅ 受试者 Experiment_for_recognize 数据预加载完成[0m
[36mℹ️ 训练样本: 0, 测试样本: 0[0m
[36m💾 当前显存使用: 1.21 GB[0m

[92m===========[0m
[1m[92m✅ 数据预加载完成 ✅[0m
[92m===========[0m

[92m✅ 成功预加载: 29 个受试者[0m
[1m[95m💾 最终显存使用: 1.21 GB[0m
根据配置,不使用Visdom可视化

[95m──────────────────────────────────────────────────[0m
[1m[95m🎯 单受试者训练模式[0m
[95m──────────────────────────────────────────────────[0m
[36mℹ️ 训练受试者: 011[0m

========================================
【当前处理受试者】: 011
========================================
组别: 大样本组(>8个测试样本)
[92m🚀 使用GPU预加载数据[0m
[36mℹ️ 预加载数据 - 训练样本: 111, 测试样本: 20[0m

011测试标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0]
TensorBoard日志目录已存在: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8/011/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8/011/logs

[94m──────────────────────────────────────────────────[0m
[1m[94m🤖 模型初始化[0m
[94m──────────────────────────────────────────────────[0m
[36m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [36m- 通过小波变换实现多频响应特征提取[0m 🌊
  [36m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [36m- 实现非常大的感受野而不会过度参数化[0m 📡
  [36m- 特别适合微表情的频域特征分析[0m 📊
[33m🚫 未启用跨分支交互[0m
  [36m- 保持原始三分支独立处理[0m 📦
  [36m- 最快推理速度，最低内存占用[0m ⚡
  [36m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

训练过程出错: only one element tensors can be converted to Python scalars
详细错误信息: Traceback (most recent call last):
  File "train_classify_SKD_TSTSANSAMM.py", line 1622, in <module>
    results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions_SAMM.py", line 1988, in main_SKD_TSTSAN_with_Aug_with_SKD
    X_train = torch.Tensor(X_train).permute(0, 3, 1, 2)
ValueError: only one element tensors can be converted to Python scalars


正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用

[96m──────────────────────────────────────────────────[0m
[1m[96m🚀 GPU数据预加载配置[0m
[96m──────────────────────────────────────────────────[0m
[92m✅ GPU数据预加载功能已启用[0m
[36mℹ️ 将在训练开始前预加载所有数据到GPU显存[0m
[36mℹ️ 这将显著提升训练速度，但需要足够的GPU显存[0m
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[95m──────────────────────────────────────────────────[0m
[1m[95m📊 损失函数配置[0m
[95m──────────────────────────────────────────────────[0m
[92m⚖️ 使用加权Focal Loss (3分类)[0m
[36m🎯 类别权重: ['0.3309', '0.0956', '0.5735'][0m
开始训练...

[96m──────────────────────────────────────────────────[0m
[1m[96m🚀 GPU数据预加载器初始化[0m
[96m──────────────────────────────────────────────────[0m
[36mℹ️ 数据集路径: /home/<USER>/data/ajq/SKD-TSTSAN-data/SAMM_LOSO_full[0m
[36mℹ️ 分类数量: 3[0m
[36mℹ️ 目标设备: cuda[0m

[92m======================[0m
[1m[92m🚀 开始预加载SAMM数据集到GPU显存 🚀[0m
[92m======================[0m

[36m📊 正在估算数据集显存需求...[0m
[92m📈 估算结果: 3668 个样本[0m
[92m💾 预计显存需求: 1.20 GB[0m
[36mℹ️ GPU总显存: 79.25 GB[0m
[36mℹ️ 可用显存: 63.40 GB[0m
[36mℹ️ 发现 29 个受试者: ['006', '007', '009', '010', '011', '012', '013', '014', '015', '016', '017', '018', '019', '020', '021', '022', '023', '024', '026', '028', '030', '031', '032', '033', '034', '035', '036', '037', 'Experiment_for_recognize'][0m
[36m📊 预加载进度: 1/29 - 006[0m
[36m📥 正在预加载受试者 006 的数据...[0m
[92m✅ 受试者 006 数据预加载完成[0m
[36mℹ️ 训练样本: 120, 测试样本: 11[0m
[36m💾 当前显存使用: 0.04 GB[0m
[36m📊 预加载进度: 2/29 - 007[0m
[36m📥 正在预加载受试者 007 的数据...[0m
[92m✅ 受试者 007 数据预加载完成[0m
[36mℹ️ 训练样本: 123, 测试样本: 8[0m
[36m💾 当前显存使用: 0.09 GB[0m
[36m📊 预加载进度: 3/29 - 009[0m
[36m📥 正在预加载受试者 009 的数据...[0m
[92m✅ 受试者 009 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.13 GB[0m
[36m📊 预加载进度: 4/29 - 010[0m
[36m📥 正在预加载受试者 010 的数据...[0m
[92m✅ 受试者 010 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.17 GB[0m
[36m📊 预加载进度: 5/29 - 011[0m
[36m📥 正在预加载受试者 011 的数据...[0m
[92m✅ 受试者 011 数据预加载完成[0m
[36mℹ️ 训练样本: 111, 测试样本: 20[0m
[36m💾 当前显存使用: 0.22 GB[0m
[36m📊 预加载进度: 6/29 - 012[0m
[36m📥 正在预加载受试者 012 的数据...[0m
[92m✅ 受试者 012 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.26 GB[0m
[36m📊 预加载进度: 7/29 - 013[0m
[36m📥 正在预加载受试者 013 的数据...[0m
[92m✅ 受试者 013 数据预加载完成[0m
[36mℹ️ 训练样本: 125, 测试样本: 6[0m
[36m💾 当前显存使用: 0.30 GB[0m
[36m📊 预加载进度: 8/29 - 014[0m
[36m📥 正在预加载受试者 014 的数据...[0m
[92m✅ 受试者 014 数据预加载完成[0m
[36mℹ️ 训练样本: 121, 测试样本: 10[0m
[36m💾 当前显存使用: 0.34 GB[0m
[36m📊 预加载进度: 9/29 - 015[0m
[36m📥 正在预加载受试者 015 的数据...[0m
[92m✅ 受试者 015 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.39 GB[0m
[36m📊 预加载进度: 10/29 - 016[0m
[36m📥 正在预加载受试者 016 的数据...[0m
[92m✅ 受试者 016 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 0.43 GB[0m
[36m📊 预加载进度: 11/29 - 017[0m
[36m📥 正在预加载受试者 017 的数据...[0m
[92m✅ 受试者 017 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.47 GB[0m
[36m📊 预加载进度: 12/29 - 018[0m
[36m📥 正在预加载受试者 018 的数据...[0m
[92m✅ 受试者 018 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.52 GB[0m
[36m📊 预加载进度: 13/29 - 019[0m
[36m📥 正在预加载受试者 019 的数据...[0m
[92m✅ 受试者 019 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.56 GB[0m
[36m📊 预加载进度: 14/29 - 020[0m
[36m📥 正在预加载受试者 020 的数据...[0m
[92m✅ 受试者 020 数据预加载完成[0m
[36mℹ️ 训练样本: 127, 测试样本: 4[0m
[36m💾 当前显存使用: 0.60 GB[0m
[36m📊 预加载进度: 15/29 - 021[0m
[36m📥 正在预加载受试者 021 的数据...[0m
[92m✅ 受试者 021 数据预加载完成[0m
[36mℹ️ 训练样本: 129, 测试样本: 2[0m
[36m💾 当前显存使用: 0.65 GB[0m
[36m📊 预加载进度: 16/29 - 022[0m
[36m📥 正在预加载受试者 022 的数据...[0m
[92m✅ 受试者 022 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 0.69 GB[0m
[36m📊 预加载进度: 17/29 - 023[0m
[36m📥 正在预加载受试者 023 的数据...[0m
[92m✅ 受试者 023 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.73 GB[0m
[36m📊 预加载进度: 18/29 - 024[0m
[36m📥 正在预加载受试者 024 的数据...[0m
[92m✅ 受试者 024 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.78 GB[0m
[36m📊 预加载进度: 19/29 - 026[0m
[36m📥 正在预加载受试者 026 的数据...[0m
[92m✅ 受试者 026 数据预加载完成[0m
[36mℹ️ 训练样本: 122, 测试样本: 9[0m
[36m💾 当前显存使用: 0.82 GB[0m
[36m📊 预加载进度: 20/29 - 028[0m
[36m📥 正在预加载受试者 028 的数据...[0m
[92m✅ 受试者 028 数据预加载完成[0m
[36mℹ️ 训练样本: 129, 测试样本: 2[0m
[36m💾 当前显存使用: 0.86 GB[0m
[36m📊 预加载进度: 21/29 - 030[0m
[36m📥 正在预加载受试者 030 的数据...[0m
[92m✅ 受试者 030 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.91 GB[0m
[36m📊 预加载进度: 22/29 - 031[0m
[36m📥 正在预加载受试者 031 的数据...[0m
[92m✅ 受试者 031 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 0.95 GB[0m
[36m📊 预加载进度: 23/29 - 032[0m
[36m📥 正在预加载受试者 032 的数据...[0m
[92m✅ 受试者 032 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 0.99 GB[0m
[36m📊 预加载进度: 24/29 - 033[0m
[36m📥 正在预加载受试者 033 的数据...[0m
[92m✅ 受试者 033 数据预加载完成[0m
[36mℹ️ 训练样本: 126, 测试样本: 5[0m
[36m💾 当前显存使用: 1.03 GB[0m
[36m📊 预加载进度: 25/29 - 034[0m
[36m📥 正在预加载受试者 034 的数据...[0m
[92m✅ 受试者 034 数据预加载完成[0m
[36mℹ️ 训练样本: 128, 测试样本: 3[0m
[36m💾 当前显存使用: 1.08 GB[0m
[36m📊 预加载进度: 26/29 - 035[0m
[36m📥 正在预加载受试者 035 的数据...[0m
[92m✅ 受试者 035 数据预加载完成[0m
[36mℹ️ 训练样本: 123, 测试样本: 8[0m
[36m💾 当前显存使用: 1.12 GB[0m
[36m📊 预加载进度: 27/29 - 036[0m
[36m📥 正在预加载受试者 036 的数据...[0m
[92m✅ 受试者 036 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 1.16 GB[0m
[36m📊 预加载进度: 28/29 - 037[0m
[36m📥 正在预加载受试者 037 的数据...[0m
[92m✅ 受试者 037 数据预加载完成[0m
[36mℹ️ 训练样本: 130, 测试样本: 1[0m
[36m💾 当前显存使用: 1.21 GB[0m
[36m📊 预加载进度: 29/29 - Experiment_for_recognize[0m
[36m📥 正在预加载受试者 Experiment_for_recognize 的数据...[0m
[92m✅ 受试者 Experiment_for_recognize 数据预加载完成[0m
[36mℹ️ 训练样本: 0, 测试样本: 0[0m
[36m💾 当前显存使用: 1.21 GB[0m

[92m===========[0m
[1m[92m✅ 数据预加载完成 ✅[0m
[92m===========[0m

[92m✅ 成功预加载: 29 个受试者[0m
[1m[95m💾 最终显存使用: 1.21 GB[0m
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: 011
========================================
组别: 大样本组(>8个测试样本)
[92m🚀 使用GPU预加载数据[0m
[36mℹ️ 预加载数据 - 训练样本: 111, 测试样本: 20[0m

011测试标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0]
TensorBoard日志目录已存在: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8/011/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8/011/logs

[94m──────────────────────────────────────────────────[0m
[1m[94m🤖 模型初始化[0m
[94m──────────────────────────────────────────────────[0m
[36m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [36m- 通过小波变换实现多频响应特征提取[0m 🌊
  [36m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [36m- 实现非常大的感受野而不会过度参数化[0m 📡
  [36m- 特别适合微表情的频域特征分析[0m 📊
[33m🚫 未启用跨分支交互[0m
  [36m- 保持原始三分支独立处理[0m 📦
  [36m- 最快推理速度，最低内存占用[0m ⚡
  [36m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

训练过程出错: expected Tensor as element 111 in argument 0, but got numpy.ndarray
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANSAMM.py", line 1622, in <module>
    results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions_SAMM.py", line 1995, in main_SKD_TSTSAN_with_Aug_with_SKD
    X_train = torch.stack(X_train)
TypeError: expected Tensor as element 111 in argument 0, but got numpy.ndarray


【实验结果报告】
================================================================================

================================================================================
【实验结果报告 - 3分类 (正性、负性、惊讶)】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn_h8
分类方案: 3分类 (正性、负性、惊讶)
时间: 2025-08-03 14:04:30
总训练时间: 00:01:26
数据集: SAMM_LOSO_full


【系统环境】
操作系统: 💻 Linux 6.8.0-1031-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 62.7GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU数量: 1
训练模式: 单设备

【模型配置】
模型架构: SKD_TSTSAN
分类数量: 3
是否使用预训练: 是
预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【数据增强配置】
使用训练数据增强: 是
使用测试数据增强: 是
旋转角度范围: 3,8
训练增强倍数: 7,2,10
测试增强倍数: 7,2,10
测试数据镜像训练: 启用
镜像训练受试者: 017,014,007,020,006

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 0.0000
- UAR分数: 0.0000

【各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
   算术平均       nan        nan        nan    

    注意     算术平均值仅供参考，总体性能以上方报告的UF1/UAR为准

================================================================================

================================================================================

【邮件通知】正在发送训练结果...
